# 智链微信小程序开发文档

## 项目概述

智链是一款基于微信小程序平台的用户-企业名片应用，提供便捷的个人名片管理、分享和收藏功能，同时展示企业信息和产品中心。

### 技术栈
- 前端：微信小程序原生开发
- 后端：Node.js
- 数据库：MySQL 8.0
  - 数据库地址：112.126.28.188:3306
  - 数据库名：zhi_lian
  - 用户名：zhi_lian
  - 密码：rD6YaJYpybweKsTn
  - root密码：Qps7b8wmhJHjTpbD
- 后端部署地址：112.126.28.188:3002

## 功能需求

### 1. 用户登录
- 基于微信小程序接口实现无感登录
- 用户首次登录自动创建账号

### 2. 个人名片管理
- 名片信息展示
- 名片信息编辑
  - 基本信息：头像（选填，可获取微信头像）、姓名（必填）、公司（必填，支持自动完成）、职位（选填）、行业（选填）
  - 联系方式：手机（选填）、微信（选填）、邮箱（选填）、座机（选填）、网址（选填）、地址（选填）
  - 业务简介（选填）
- 名片分享功能
- **公司字段自动完成功能**：
  - 用户输入公司名称时，自动搜索数据库中匹配的企业
  - 支持模糊匹配，优先显示以关键词开头的企业
  - 下拉列表支持滚动选择，点击外部区域或清空按钮时收起
  - 使用自定义组件`autocomplete-input`实现

### 3. 名片夹功能
- 收集他人名片
- 查看收集的名片列表
- 查看名片详情

### 4. 企业页面
- 与个人关联的企业信息展示
- 根据用户所属企业跳转到对应企业的专属页面
- 企业页面集成在小程序内部

### 5. 产品中心
- 与个人关联的企业产品展示
- 根据用户所属企业跳转到对应的产品中心专属页面
- 产品中心页面集成在小程序内部
- 支持产品列表展示和产品详情查看
- 支持分享整个产品中心或单个产品详情
- 产品卡片支持点击整个卡片区域跳转到产品详情
- 产品中心页面右上角添加"分享此页"按钮

## 系统架构

### 前端架构
- 采用微信小程序原生开发
- 页面结构：
  - 个人页面（首页，显示名片和名片夹入口，包含登录状态同步机制）
  - 企业页面（根据company_code动态跳转到对应企业页面）
  - 产品中心页面（根据company_code动态跳转到对应产品中心页面）
- 底部导航栏：个人、企业、产品中心

### 后端架构
- Node.js提供RESTful API服务
- 主要API模块：
  - 用户认证模块（包含token验证和401错误处理）
  - 名片管理模块
  - 名片夹管理模块
  - 企业关联模块

### 数据库设计

#### 用户表(users)
| 字段名 | 类型 | 描述 | 约束 |
| --- | --- | --- | --- |
| id | INT | 用户ID | 主键, 自增 |
| openid | VARCHAR(50) | 微信openid | 唯一, 非空 |
| unionid | VARCHAR(50) | 微信unionid | 可空 |
| session_key | VARCHAR(128) | 微信会话密钥 | 可空 |
| company_code | VARCHAR(50) | 企业唯一标识码 | 可空 |
| created_at | DATETIME | 创建时间 | 非空 |
| updated_at | DATETIME | 更新时间 | 非空 |

#### 名片表(business_cards)
| 字段名 | 类型 | 描述 | 约束 |
| --- | --- | --- | --- |
| id | INT | 名片ID | 主键, 自增 |
| user_id | INT | 关联用户ID | 外键, 非空 |
| avatar | VARCHAR(255) | 头像URL | 可空 |
| name | VARCHAR(50) | 姓名 | 非空 |
| company | VARCHAR(100) | 公司 | 非空 |
| position | VARCHAR(50) | 职位 | 非空 |
| industry | VARCHAR(50) | 行业 | 可空 |
| mobile | VARCHAR(20) | 手机号 | 非空 |
| wechat | VARCHAR(50) | 微信号 | 可空 |
| email | VARCHAR(100) | 邮箱 | 可空 |
| phone | VARCHAR(20) | 座机 | 可空 |
| website | VARCHAR(255) | 网址 | 可空 |
| address | VARCHAR(255) | 地址 | 可空 |
| introduction | TEXT | 业务简介 | 可空 |
| company_code | VARCHAR(50) | 企业唯一标识码 | 可空 |
| created_at | DATETIME | 创建时间 | 非空 |
| updated_at | DATETIME | 更新时间 | 非空 |

#### 名片夹表(card_collections)
| 字段名 | 类型 | 描述 | 约束 |
| --- | --- | --- | --- |
| id | INT | 收藏ID | 主键, 自增 |
| user_id | INT | 收藏者用户ID | 外键, 非空 |
| card_id | INT | 被收藏的名片ID | 外键, 非空 |
| created_at | DATETIME | 创建时间 | 非空 |

#### 企业表(companies)
| 字段名 | 类型 | 描述 | 约束 |
| --- | --- | --- | --- |
| id | INT | 企业ID | 主键, 自增 |
| company_code | VARCHAR(50) | 企业唯一标识码 | 唯一, 非空 |
| name | VARCHAR(100) | 企业名称 | 非空 |
| enterprise_page | VARCHAR(255) | 企业页面路径 | 可空 |
| products_page | VARCHAR(255) | 产品中心页面路径 | 可空 |
| enterprise_name | VARCHAR(30) | 自定义企业页面名称 | 默认'企业' |
| products_name | VARCHAR(30) | 自定义产品中心名称 | 默认'产品中心' |
| enterprise_share_image | VARCHAR(255) | 企业页面分享图URL | 可空 |
| products_share_image | VARCHAR(255) | 产品中心分享图URL | 可空 |
| created_at | DATETIME | 创建时间 | 非空 |
| updated_at | DATETIME | 更新时间 | 非空 |

## 页面流程

### 1. 小程序入口流程
- 方式一：用户搜索小程序进入
  - 微信登录 → 个人页面(首页)
- 方式二：通过分享卡片进入
  - 进入分享者的名片页面 → 可选择收藏名片 → 微信登录(如未登录) → 名片收藏成功

### 2. 名片管理流程
- 个人页面 → 点击名片 → 进入编辑页面 → 填写/修改信息 → 保存 → 更新名片展示
- 个人页面 → 点击"创建新名片" → 进入创建名片页面 → 填写信息 → 保存 → 新名片创建成功
- 个人页面 → 点击"切换名片" → 进入切换名片页面 → 选择要使用的名片 → 设为当前名片

### 3. 名片分享流程
- 个人页面 → 点击分享 → 生成微信小程序卡片 → 分享给好友
- **重要**：所有分享的名片链接都包含`fromShare=true`参数，确保分享进入的名片能正确显示导航栏

### 4. 名片收藏流程
- 查看他人名片 → 点击"收下名片" → 名片加入名片夹
- 个人页面 → 点击"名片夹" → 查看收藏的名片列表 → 点击具体名片查看详情

### 5. 企业/产品页面访问流程
- 用户登录 → 获取用户信息（包含company_code）→ 点击"企业"/"产品中心" → 根据company_code获取对应页面路径 → 跳转到对应企业的专属页面

## API接口设计

### 1. 用户认证
- `POST /api/auth/login`: 微信登录接口
  - 请求参数：code（微信临时登录凭证）
  - 返回数据：用户ID、openid、company_code、是否有名片信息

### 2. 名片管理
- `GET /api/card`: 获取当前用户名片信息
- `POST /api/card`: 创建/更新用户名片
  - 创建/更新用户名片时，同步更新users表中的company_code
- `GET /api/card/:id`: 获取指定ID的名片信息

### 3. 名片夹管理
- `POST /api/collection`: 收藏名片
  - 请求参数：card_id（被收藏的名片ID）
- `GET /api/collection`: 获取当前用户的名片夹列表
- `DELETE /api/collection/:id`: 删除收藏的名片

### 4. 企业页面相关
- `GET /api/company/info`: 获取当前用户所属企业的页面路径
  - 根据用户的company_code返回对应企业页面和产品中心页面的路径
  - 返回自定义名称字段(enterpriseName, productsName)和分享图片字段(enterpriseShareImage, productsShareImage)
- `PUT /api/company/pages`: 更新企业页面和产品中心页面的路径
- `PUT /api/company/custom-names`: 更新企业和产品中心的自定义名称
  - 请求参数：enterpriseName(可选), productsName(可选), companyCode(可选)
  - 不传companyCode时使用当前用户关联的企业
- `PUT /api/company/share-images`: 更新企业和产品中心的分享图片
  - 请求参数：enterpriseShareImage(可选), productsShareImage(可选), companyCode(必填)
  - 需要管理员权限

**注意**：企业页面和产品中心页面的路径通常通过直接操作数据库来维护，而非通过应用接口。这是因为页面路径的更新是一个低频且需要谨慎操作的管理任务，一般由开发人员直接在数据库中操作。

## 项目实际目录结构

### 前端目录结构

```
project-root/
│
├── pages/                   # 小程序页面
│   ├── personal/            # 个人页面(首页)
│   │   ├── index.js         # 首页逻辑（包含登录状态同步和token过期处理）
│   │   ├── index.wxml       # 首页结构
│   │   ├── index.wxss       # 首页样式
│   │   ├── edit.js          # 名片编辑页逻辑
│   │   ├── edit.wxml        # 名片编辑页结构
│   │   ├── edit.wxss        # 名片编辑页样式
│   │   ├── create-card.js   # 创建名片页逻辑
│   │   ├── create-card.wxml # 创建名片页结构
│   │   ├── create-card.wxss # 创建名片页样式
│   │   ├── switch-card.js   # 切换名片页逻辑
│   │   ├── switch-card.wxml # 切换名片页结构
│   │   ├── switch-card.wxss # 切换名片页样式
│   │   ├── card-folder.js   # 名片夹页逻辑（包含401错误处理）
│   │   ├── card-folder.wxml # 名片夹页结构
│   │   ├── card-folder.wxss # 名片夹页样式
│   │   ├── custom-settings.js   # 自定义设置页逻辑
│   │   ├── custom-settings.wxml # 自定义设置页结构
│   │   ├── custom-settings.wxss # 自定义设置页样式
│   │   └── custom-settings.json # 自定义设置页配置
│   ├── card-detail/         # 名片详情页
│   │   ├── index.js         # 详情页逻辑
│   │   ├── index.wxml       # 详情页结构
│   │   └── index.wxss       # 详情页样式
│   ├── enterprise/          # 企业页面目录
│   │   ├── index.js         # 企业页面入口逻辑
│   │   ├── index.wxml       # 企业页面入口结构
│   │   ├── index.wxss       # 企业页面入口样式
│   │   ├── default.js       # 默认企业页面逻辑
│   │   ├── default.wxml     # 默认企业页面结构
│   │   ├── default.wxss     # 默认企业页面样式
│   │   └── unlogged-display.js # 未登录时的企业页面
│   ├── products/            # 产品中心目录
│   │   ├── index.js         # 产品中心入口逻辑
│   │   ├── index.wxml       # 产品中心入口结构
│   │   ├── index.wxss       # 产品中心入口样式
│   │   ├── detail.js        # 产品详情页逻辑
│   │   ├── detail.wxml      # 产品详情页结构
│   │   ├── detail.wxss      # 产品详情页样式
│   │   ├── default.js       # 默认产品页面逻辑
│   │   ├── default.wxml     # 默认产品页面结构
│   │   ├── default.wxss     # 默认产品页面样式
│   │   └── unlogged-display.js # 未登录时的产品页面
│   ├── login/               # 登录页面
│   │   ├── index.js         # 登录页面逻辑（优化登录成功后的跳转流程）
│   │   ├── index.wxml       # 登录页面结构
│   │   └── index.wxss       # 登录页面样式
│   └── agreement/           # 协议页面
│       ├── service-agreement.js # 服务协议页面
│       └── privacy-policy.js    # 隐私政策页面
│
├── components/              # 自定义组件
│   ├── business-card/       # 名片组件
│   │   ├── index.js         # 完整名片组件逻辑
│   │   ├── index.wxml       # 完整名片组件结构
│   │   ├── index.wxss       # 完整名片组件样式
│   │   └── index.json       # 完整名片组件配置
│   ├── form-items/          # 表单组件
│   │   ├── input-item.js    # 输入框组件逻辑
│   │   ├── input-item.wxml  # 输入框组件结构
│   │   └── input-item.wxss  # 输入框组件样式
│   └── contact-popup/       # 联系方式弹窗组件
│       ├── index.js         # 联系方式弹窗逻辑
│       ├── index.wxml       # 联系方式弹窗结构
│       ├── index.wxss       # 联系方式弹窗样式
│       └── index.json       # 联系方式弹窗配置
│
├── custom-tab-bar/          # 自定义底部导航栏
│   ├── index.js             # 导航栏逻辑
│   ├── index.wxml           # 导航栏结构
│   ├── index.wxss           # 导航栏样式
│   └── index.json           # 导航栏配置
│
├── utils/                   # 工具类
│   ├── request.js           # 网络请求封装（包含401错误统一处理）
│   ├── auth.js              # 认证相关
│   ├── company.js           # 企业信息处理
│   ├── card.js              # 名片相关API和工具
│   ├── common.js            # 通用工具方法
│   ├── collection.js        # 名片收藏相关API和工具
│   └── share-image.js       # 分享图生成工具
│
├── store/                   # 数据管理
│   ├── user.js              # 用户数据
│   ├── card.js              # 名片数据
│   └── company.js           # 企业数据
│
├── images/                  # 静态图片资源
│   └── tab/                 # 导航栏图标
│       ├── personal.png     # 个人未选中图标
│       ├── personal-active.png  # 个人选中图标
│       ├── enterprise.png   # 企业未选中图标
│       ├── enterprise-active.png # 企业选中图标
│       ├── products.png     # 产品中心未选中图标
│       ├── products-active.png  # 产品中心选中图标
│       ├── card-folder.png      # 名片夹未选中图标
│       └── card-folder-active.png # 名片夹选中图标
│
├── app.js                   # 应用入口（包含登录状态管理和token验证）
├── app.json                 # 全局配置
├── app.wxss                 # 全局样式
├── project.config.json      # 项目配置
└── sitemap.json             # 站点地图配置
```

### 后端服务架构

```
server/
│
├── controllers/             # 控制器
│   ├── auth.controller.js   # 认证控制器
│   ├── card.controller.js   # 名片控制器
│   ├── collection.controller.js # 名片夹控制器
│   ├── company.controller.js # 企业控制器（支持通过companyCode查询）
│   └── upload.controller.js  # 文件上传控制器
│
├── models/                  # 数据模型
│   ├── index.js             # 数据库连接初始化和关联定义
│   ├── user.model.js        # 用户模型
│   ├── card.model.js        # 名片模型
│   ├── collection.model.js  # 名片收藏模型
│   └── company.model.js     # 企业模型
│
├── routes/                  # 路由
│   ├── index.js             # 路由索引文件
│   ├── auth.routes.js       # 认证路由
│   ├── card.routes.js       # 名片路由
│   ├── collection.routes.js # 名片夹路由
│   ├── company.routes.js    # 企业路由（支持companyCode参数）
│   └── upload.routes.js     # 文件上传路由
│
├── config/                  # 配置文件
│   ├── db.config.js         # 数据库配置
│   └── app.config.js        # 应用配置
│
├── middlewares/             # 中间件
│   ├── auth.middleware.js   # 认证中间件
│   └── error.middleware.js  # 错误处理中间件
│
├── utils/                   # 工具类
│   ├── common.js            # 通用工具函数
│   └── weapp.js             # 微信小程序相关API工具
│
├── app.js                   # 应用入口
├── .env                     # 环境变量配置
├── package.json             # 项目依赖
└── ecosystem.config.js      # PM2配置文件
```

## 小程序配置说明

### app.json 配置

小程序的全局配置文件app.json中定义了以下内容：

1. 页面路径配置：
   - 首页：pages/personal/index
   - 企业页面：pages/enterprise/index（统一处理所有企业页面逻辑）
   - 产品中心：pages/products/index（统一处理所有产品页面逻辑）
   - 产品详情页：pages/products/detail
   - 名片编辑页：pages/personal/edit
   - 名片夹页：pages/personal/card-folder
   - 名片详情页：pages/card-detail/index
   - 默认企业页：pages/enterprise/default
   - 默认产品页：pages/products/default
   - 协议页面：pages/agreement/service-agreement, pages/agreement/privacy-policy
   - 登录页面：pages/login/index
   - 未登录企业展示页：pages/enterprise/unlogged-display
   - 未登录产品展示页：pages/products/unlogged-display
   - 自定义设置页：pages/personal/custom-settings
   - 切换名片页：pages/personal/switch-card
   - 创建名片页：pages/personal/create-card

2. 窗口配置：
   - 导航栏文字颜色：白色
   - 导航栏标题：智链
   - 导航栏背景色：#3E7FFF
   - 窗口背景色：#F8FAFF

3. 底部导航栏配置：
   - 使用自定义导航栏：custom: true
   - 导航项：
     - 个人页面
     - 名片夹页面

## 用户与企业关联逻辑

1. 用户创建/更新名片时，系统会根据用户填写的公司名称生成或查找对应的company_code，并更新到用户记录中
2. 用户点击"企业"或"产品中心"时：
   - 小程序从本地存储或全局数据中获取用户的company_code
   - 请求后端API获取对应企业的页面路径信息
   - 根据返回的路径，通过小程序的页面跳转API跳转到对应的企业专属页面
3. 分享进入的名片详情页面（card-detail）：
   - 通过`fromShare=true`或`fromShared=true`参数识别为分享进入
   - 点击"企业"或"产品中心"时跳转到对应页面，并传递相应参数显示导航栏
   - 企业页面和产品中心页面统一处理所有逻辑，根据参数决定是否显示导航栏
   - 产品中心模块只有在企业有实际产品时才显示（通过API检查产品列表）
   - 用户从企业或产品中心点击导航栏按钮时，可以返回到分享页面

## 页面导航栏功能

### 导航栏显示逻辑
- **产品中心页面**、**产品详情页面**、**企业页面**均配备统一的导航栏
- **显示条件**：所有进入方式都显示导航栏（包括个人视角和分享视角）
- **位置**：固定在屏幕底部，距离底部80rpx
- **UI规格**：紧凑设计，适配iPad等大屏设备

### 导航栏按钮配置
#### 分享视角进入时：
- **返回名片按钮**：当有cardId时显示，点击返回分享的名片页面
- **返回主页按钮**：当有companyCode时显示，跳转到企业页面
- **产品中心按钮**：在详情页和企业页显示，跳转到产品中心
- **分享按钮**：所有页面都显示，调起微信分享功能

#### 个人视角进入时：
- **个人页面按钮**：返回个人首页（使用switchTab）
- **企业页面按钮**：当有企业信息时显示，跳转到企业页面
- **产品中心按钮**：在详情页和企业页显示，跳转到产品中心
- **分享按钮**：所有页面都显示，调起微信分享功能

### 导航栏技术实现
- **切换功能**：支持显示/隐藏切换，默认展开状态
- **响应式设计**：按钮图标32rpx，文字20rpx，适配不同屏幕
- **事件处理**：统一的跳转逻辑，支持参数传递和状态保持
4. 如果用户没有关联企业(company_code为空)或企业页面路径未设置：
   - 显示默认企业页面，提示用户先完善个人名片信息

## 自定义设置功能

自定义设置页面（custom-settings）提供了企业和产品中心名称自定义功能：

1. 功能概述：
   - 允许用户自定义企业和产品中心的显示名称
   - 预览企业和产品中心的分享图片
   - 提供说明文字，引导用户联系客服修改分享图片

2. 页面结构：
   - 自定义名称表单：包含企业名称和产品中心名称两个输入框
   - 说明文字：解释自定义名称的用途和生效范围
   - 分享图预览：展示企业和产品中心的分享图片
   - 保存按钮：提交用户的自定义设置

3. 数据流转：
   - 页面加载时从API获取当前自定义名称设置
   - 用户修改后点击保存，将新设置提交到后端
   - 保存成功后设置needRefresh标志，返回个人页面时自动刷新数据
   - 自定义名称将应用于分享标题、按钮文本和页面显示

4. 技术实现：
   - 使用表单组件收集用户输入
   - 使用flex布局和适当的样式确保良好的用户体验
   - 通过API与后端交互，保存和获取自定义设置
   - 通过页面间通信机制，确保设置更新后相关页面得到刷新

5. 关键实现细节：
   - **数据更新机制**：在`custom-settings.js`的`saveSettings`方法中设置`needRefresh`标志，通过`getCurrentPages()`获取上一页实例，设置其`data.needRefresh = true`
   - **全局状态同步**：在`store/company.js`中实现了`updateCompanyCustomNames`方法，保存成功后会同步更新全局状态
   - **页面监听更新**：在`personal/index.js`的`onShow`方法中检测`needRefresh`标志，如为true则重新加载数据
   - **底部导航栏更新**：自定义底部导航栏组件通过监听全局状态变化，实时更新显示的名称
   - **分享图预览**：使用图片预览组件展示企业和产品中心分享图，点击可查看大图
   - **字符长度限制**：自定义名称最多支持30个字符，超过部分会被截断
   - **自动命名规则**：创建名片时自动为企业设置"[公司名]AI名片"和"[公司名]产品中心"作为自定义名称

6. 测试要点：
   - 保存自定义名称后返回个人页面，验证名称是否立即更新
   - 检查底部导航栏文本是否同步更新
   - 验证分享功能中的标题是否使用了自定义名称
   - 测试长文本名称的显示效果和截断处理
   - 确认未设置自定义名称时默认值的正确显示

## 注意事项与技术要点

1. 微信小程序登录流程
   - 需要调用wx.login获取临时登录凭证code
   - 后端使用code换取openid并创建用户
   - **iPad适配**：登录页面支持大屏设备，解决按钮显示和滚动问题
     - 页面布局：从固定高度改为可滚动布局（min-height: 100vh）
     - 按钮优化：防止文字换行，设置合适的最小宽度和内边距
     - 响应式设计：大屏设备下限制容器宽度并居中显示
     - 滚动支持：确保在iPad等设备上可以正常滚动查看所有内容

2. 名片分享功能
   - 使用小程序自带的onShareAppMessage接口
   - 通过本地存储（wx.setStorageSync）和URL参数传递，实现页面间的状态保持

3. 数据安全性
   - 用户敏感信息加密存储
   - API接口权限控制

4. **Token过期处理机制（重要）**
   - 统一401错误处理：在`utils/request.js`中彻底清理所有相关缓存
   - 应用级状态管理：`app.js`中添加token验证和状态清理机制
   - 页面级错误处理：个人页面和名片夹页面都有401错误处理逻辑
   - 模态框状态管理：防止token过期提示模态框残留，使用延迟检查机制
   - 登录状态同步：`syncLoginStatus()`方法确保页面状态与全局状态一致

5. 性能优化
   - 小程序包体积控制
   - 首屏加载速度优化
   - 静态资源缓存策略
   - 图片显示优化：使用object-fit:contain确保图片完整显示
   - 移除不必要的加载动画，减少网络请求
   
5. 页面路由与动态跳转
   - 根据场景使用不同跳转方式：switchTab、navigateTo和redirectTo
   - 分享页面使用redirectTo避免从右往左的动画效果
   - 小程序页面路径不能动态生成，需要在app.json中预先注册
   - 页面间通过参数和本地存储传递数据

6. 多名片管理
   - 支持创建多张名片
   - 提供切换名片功能，可设置默认名片

7. **自动完成组件（autocomplete-input）**
   - **功能**：为公司字段提供智能输入建议
   - **位置**：`components/autocomplete-input/`
   - **特性**：
     - 模糊搜索：支持企业名称的模糊匹配
     - 防抖搜索：300ms延迟，避免频繁请求
     - 智能排序：优先显示以关键词开头的企业
     - 用户体验：支持滚动选择，点击外部收起
     - 响应式：适配不同屏幕尺寸
   - **API集成**：调用`GET /company/search`接口
   - **使用方式**：在创建名片和编辑名片页面中集成

7. **UI设计规范（重要）**
   - **名片模块卡片设计**：
     - 左侧封面图片：5:4长宽比（200rpx × 160rpx）
     - 右侧内容：企业自定义名称、企业名、查看详情按钮
     - 自定义名称支持多行显示，不使用省略号截断
     - 查看详情按钮位于右下角，包含同色调箭头">"
   - **产品中心页面**：
     - 标题和分享按钮布局：标题自适应宽度，分享按钮固定180rpx宽度
     - 分享按钮完整显示"分享此页"文字和图标
   - **页面统一性**：
     - 企业页面和产品中心页面使用统一的逻辑处理
     - 删除了shared-products独立页面，统一使用products/index
   - 通过后端API同步名片状态

7. 图片显示优化
   - 企业和产品中心模块使用正方形预览区域
   - 图片使用object-fit:contain确保完整显示
   - 优化图片加载体验，减少白屏时间

8. UI自适应布局
   - 按钮文字区域高度自动适应内容长度
   - 当一排按钮中有按钮文字需要两行显示时，整排按钮高度会自动调整
   - 长文本自动截断并显示省略号，但不会影响图标位置
   - 自定义企业名称和产品中心名称支持最多30个字符

9. **错误处理和状态管理（重要）**
   - **401错误统一处理**：所有API请求的401错误都会触发统一的登录状态清理
   - **登录状态同步**：页面显示时自动同步全局状态与页面状态
   - **模态框状态管理**：防止token过期提示模态框残留，使用延迟检查机制
   - **定时器管理**：正确清理定时器，防止内存泄漏
   - **缓存清理策略**：token失效时彻底清理所有相关缓存（token、userInfo、cardInfo、companyInfo）

## 关键技术实现

### Token过期处理机制（重要）

#### 1. 统一401错误处理
**文件**: `utils/request.js`
- 在业务错误(code=401)和HTTP错误(statusCode=401)时都会彻底清理登录状态
- 清理内容：token、userInfo、cardInfo、companyInfo、cardUpdated等所有相关缓存
- 清理本地存储：token、userInfo、cardInfo、companyInfo
- 更新TabBar状态为隐藏
- 对401错误不显示toast，让调用方统一处理

#### 2. 应用级登录状态管理
**文件**: `app.js`
- `validateToken()`: 验证后端token有效性
- `clearLoginState()`: 统一清理登录状态的方法
- `checkSession()`: 检查微信session和后端token
- 应用启动时主动验证token有效性

#### 3. 页面级状态同步
**文件**: `pages/personal/index.js`
- `syncLoginStatus()`: 强制同步全局状态与页面状态
- `handleTokenExpired()`: 统一处理token过期，包含模态框状态管理
- `onLoad()` 和 `onShow()`: 确保页面状态与全局状态一致
- 模态框防残留机制：使用延迟检查和定时器管理

#### 4. 模态框状态管理
- `isShowingTokenExpiredModal`: 标记模态框显示状态
- 延迟200ms显示模态框，期间检查用户是否已重新登录
- 定时器管理：正确清理定时器防止内存泄漏
- 自动取消机制：检测到重新登录时自动取消模态框显示

#### 5. 开发调试
开发过程中可使用以下调试命令：
```javascript
// 模拟token过期
debugTokenExpiry.simulateTokenExpiry()

// 检查状态一致性
debugTokenExpiry.checkStateConsistency()

// 检查模态框状态
debugTokenExpiry.checkModalStatus()

// 强制同步状态
debugTokenExpiry.forceSyncPageStatus()
```

## 部署说明

### 后端部署

1. 后端服务器环境：
   - Node.js 14+
   - MySQL 8.0
   - PM2进程管理

2. 部署步骤：
   - 将server目录代码上传至服务器
   - 安装依赖：`npm install`
   - 配置.env环境变量文件
   - 使用PM2启动服务：`pm2 start ecosystem.config.js`

3. 服务器配置：
   - 使用Nginx作为反向代理
   - 配置SSL证书
   - 开放3002端口

### 前端发布

1. 小程序开发工具中构建
2. 提交审核
3. 发布上线

## 开发注意事项

### 重要提醒

1. **Token过期处理**
   - 所有新增的API调用都必须包含401错误处理
   - 不要在401错误时显示技术性错误提示
   - 确保页面状态与全局状态保持同步

2. **状态管理**
   - 页面的`onShow`方法中应调用状态同步逻辑
   - 添加新的缓存数据时，记得在`clearLoginState`方法中清理
   - 使用定时器时必须在页面销毁时清理

3. **模态框使用**
   - 微信小程序的`wx.showModal`无法通过代码关闭
   - 需要使用预防性策略避免模态框残留
   - 重要提示类模态框应包含状态管理机制

4. **调试和测试**
   - 开发过程中使用提供的调试工具测试token过期场景
   - 真机测试时特别注意缓存行为与开发者工具的差异
   - 登录流程修改后必须完整测试各种场景

