<view class="container">
  <form bindsubmit="submitForm">
    <!-- 头像上传区域 -->
    <view class="avatar-section">
      <view class="avatar-wrapper" bindtap="uploadAvatar">
        <image class="avatar" src="{{formData.avatar || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="avatar-upload-icon">
          <image src="https://pic.sdtaa.com/ZhiLian/Picture/Project/camera.png" mode="aspectFit"></image>
        </view>
      </view>
      <button class="wechat-avatar-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">使用微信头像</button>
    </view>

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <view class="form-label">姓名 <text class="required">*</text></view>
          <input class="form-input" value="{{formData.name}}" placeholder="请输入姓名" data-field="name" bindinput="onInputChange" />
        </view>
        
        <view class="form-item">
          <autocomplete-input
            label="公司"
            required="{{true}}"
            value="{{formData.company}}"
            placeholder="请输入公司名称"
            searchUrl="/company/search"
            searchParam="keyword"
            displayField="name"
            valueField="name"
            minSearchLength="1"
            searchDelay="300"
            bindinput="onCompanyInput"
            bindselect="onCompanySelect"
          />
        </view>
        
        <view class="form-item">
          <view class="form-label">职位</view>
          <input class="form-input" value="{{formData.position}}" placeholder="请输入职位" data-field="position" bindinput="onInputChange" />
        </view>
        
        <view class="form-item">
          <view class="form-label">行业</view>
          <picker mode="selector" range="{{industries}}" value="{{industries.indexOf(formData.industry)}}" bindchange="onPickerChange" data-field="industry">
            <view class="picker {{formData.industry ? '' : 'placeholder'}}">
              {{formData.industry || '请选择行业'}}
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">联系方式</view>
        
        <view class="form-item">
          <view class="form-label">手机号码</view>
          <input class="form-input" type="number" value="{{formData.mobile}}" placeholder="请输入手机号码" data-field="mobile" bindinput="onInputChange" />
        </view>
        
        <view class="form-item">
          <view class="form-label">微信号</view>
          <input class="form-input" value="{{formData.wechat}}" placeholder="请输入微信号" data-field="wechat" bindinput="onInputChange" />
        </view>
        
        <view class="form-item">
          <view class="form-label">邮箱</view>
          <input class="form-input" type="text" value="{{formData.email}}" placeholder="请输入邮箱" data-field="email" bindinput="onInputChange" />
        </view>
        
        <view class="form-item">
          <view class="form-label">地址</view>
          <input class="form-input" type="text" value="{{formData.address}}" placeholder="请输入地址" data-field="address" bindinput="onInputChange" />
        </view>

        <!-- 更多选项 -->
        <view class="more-options">
          <view class="more-options-header" bindtap="toggleMoreOptions">
            <text>点击填写更多信息</text>
            <view class="arrow {{showMoreOptions ? 'arrow-up' : 'arrow-down'}}"></view>
          </view>
          
          <view class="more-options-content" wx:if="{{showMoreOptions}}">
            <view class="form-item">
              <view class="form-label">座机</view>
              <input class="form-input" type="text" value="{{formData.phone}}" placeholder="请输入座机号码" data-field="phone" bindinput="onInputChange" />
            </view>
            
            <view class="form-item">
              <view class="form-label">网址</view>
              <input class="form-input" type="text" value="{{formData.website}}" placeholder="请输入网址" data-field="website" bindinput="onInputChange" />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 业务简介 -->
      <view class="form-section">
        <view class="section-title">业务简介</view>
        
        <view class="form-item">
          <textarea class="form-textarea" value="{{formData.introduction}}" placeholder="请输入业务简介(选填)" maxlength="300" data-field="introduction" bindinput="onInputChange"></textarea>
          <view class="textarea-counter">{{formData.introduction.length || 0}}/300</view>
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-btn-wrapper">
      <button class="submit-btn" disabled="{{isSubmitting}}" bindtap="submitForm">保存</button>
    </view>
  </form>
</view> 