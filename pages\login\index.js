const app = getApp()

Page({
  data: {
    isLoggingIn: false,
    isAgreementChecked: false
  },

  onLoad() {
    // 页面加载时，检查是否已经登录
    const token = wx.getStorageSync('token')
    if (token) {
      // 已登录则跳转到个人页面（首页）
      wx.switchTab({
        url: '/pages/personal/index'
      });
    }
  },

  // 切换协议勾选状态
  toggleAgreement() {
    this.setData({
      isAgreementChecked: !this.data.isAgreementChecked
    })
  },
  
  // 跳转到服务协议页面
  goToServiceAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/service-agreement',
    });
  },
  
  // 跳转到隐私政策页面
  goToPrivacyPolicy() {
    wx.navigateTo({
      url: '/pages/agreement/privacy-policy',
    });
  },

  // 登录
  login() {
    if (this.data.isLoggingIn) return;
    
    // 登录按钮在未勾选时已经被禁用，用户无法点击
    this.setData({
      isLoggingIn: true
    })
    
    wx.showLoading({
      title: '登录中',
    })
    
    const auth = require('../../utils/auth.js')
    auth.login().then(res => {
      if (res.code === 0 && res.data) {
        console.log('登录成功，用户信息：', res.data);

        // 确保全局状态正确设置
        app.globalData.userInfo = res.data;
        app.globalData.token = app.globalData.token || wx.getStorageSync('token');

        // 更新TabBar状态
        if (app.globalData.tabBarControl) {
          app.globalData.tabBarControl.show = true;
        }

        wx.hideLoading();

        // 登录成功，立即跳转到个人页面
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1000,
          success: () => {
            // 缩短延迟时间，快速跳转
            setTimeout(() => {
              console.log('准备跳转到个人页面');
              wx.switchTab({
                url: '/pages/personal/index',
                success: () => {
                  console.log('跳转到个人页面成功');
                },
                fail: (err) => {
                  console.error('跳转到个人页面失败', err);
                }
              });
            }, 800);
          }
        });

        // 重置登录状态
        this.setData({
          isLoggingIn: false
        });
      } else {
        wx.hideLoading()
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        });

        this.setData({
          isLoggingIn: false
        });
      }
    }).catch(err => {
      console.error('登录失败', err)

      wx.hideLoading()
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      });

      this.setData({
        isLoggingIn: false
      });
    });
  }
}); 