Component({
  /**
   * 组件的属性列表
   */
  properties: {
    label: {
      type: String,
      value: ''
    },
    name: {
      type: String,
      value: ''
    },
    required: {
      type: Boolean,
      value: false
    },
    value: {
      type: String,
      value: ''
    },
    uploadText: {
      type: String,
      value: '上传图片'
    },
    count: {
      type: Number,
      value: 1
    },
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    imageUrl: '',
    isLoading: false
  },

  observers: {
    // 监听value属性变化
    'value': function(value) {
      this.setData({
        imageUrl: value
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 选择图片
    onChooseImage() {
      if (this.properties.disabled || this.data.isLoading) {
        return;
      }
      
      wx.chooseImage({
        count: this.properties.count,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          this.uploadImage(tempFilePaths[0]);
        }
      });
    },
    
    // 上传图片
    uploadImage(filePath) {
      this.setData({
        isLoading: true
      });
      
      // 这里仅做演示，实际项目中应调用后端API上传
      // 此处模拟上传过程
      setTimeout(() => {
        // 模拟上传成功
        this.setData({
          imageUrl: filePath,
          isLoading: false
        });
        
        // 触发上传完成事件
        this.triggerEvent('upload', {
          name: this.properties.name,
          value: filePath
        });
      }, 1000);
      
      // 实际项目中的上传代码如下
      /*
      const app = getApp();
      wx.uploadFile({
        url: `${app.globalData.baseUrl}/upload/avatar`,
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${app.globalData.token}`
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code === 0 && data.data) {
            this.setData({
              imageUrl: data.data.url,
              isLoading: false
            });
            
            this.triggerEvent('upload', {
              name: this.properties.name,
              value: data.data.url
            });
          } else {
            wx.showToast({
              title: data.message || '上传失败',
              icon: 'none'
            });
            this.setData({
              isLoading: false
            });
          }
        },
        fail: () => {
          wx.showToast({
            title: '上传失败，请重试',
            icon: 'none'
          });
          this.setData({
            isLoading: false
          });
        }
      });
      */
    },
    
    // 查看图片
    onPreviewImage() {
      if (this.data.imageUrl) {
        wx.previewImage({
          urls: [this.data.imageUrl],
          current: this.data.imageUrl
        });
      }
    },
    
    // 删除图片
    onDeleteImage() {
      wx.showModal({
        title: '提示',
        content: '确定要删除图片吗？',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              imageUrl: ''
            });
            
            this.triggerEvent('upload', {
              name: this.properties.name,
              value: ''
            });
          }
        }
      });
    }
  }
}) 