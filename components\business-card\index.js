Component({
  /**
   * 组件的属性列表
   */
  properties: {
    cardInfo: {
      type: Object,
      value: {}
    },
    showActions: {
      type: Boolean,
      value: false
    },
    isOwner: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    defaultAvatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-avatar.png'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 编辑名片
    onEdit() {
      this.triggerEvent('edit');
    },
    
    // 分享名片
    onShare() {
      this.triggerEvent('share');
    },
    
    // 收藏名片
    onCollect() {
      this.triggerEvent('collect');
    },
    
    // 拨打电话
    onCall() {
      const { mobile } = this.properties.cardInfo;
      if (mobile) {
        wx.makePhoneCall({
          phoneNumber: mobile
        });
      }
    },
    
    // 复制微信号
    onCopyWechat() {
      const { wechat } = this.properties.cardInfo;
      if (wechat) {
        wx.setClipboardData({
          data: wechat,
          success: () => {
            wx.showToast({
              title: '微信号已复制',
              icon: 'success'
            });
          }
        });
      }
    },
    
    // 复制邮箱
    onCopyEmail() {
      const { email } = this.properties.cardInfo;
      if (email) {
        wx.setClipboardData({
          data: email,
          success: () => {
            wx.showToast({
              title: '邮箱已复制',
              icon: 'success'
            });
          }
        });
      }
    },
    
    // 查看地址
    onViewAddress() {
      const { address } = this.properties.cardInfo;
      if (address) {
        // 可以后续集成地图查看
        wx.setClipboardData({
          data: address,
          success: () => {
            wx.showToast({
              title: '地址已复制',
              icon: 'success'
            });
          }
        });
      }
    }
  }
}) 