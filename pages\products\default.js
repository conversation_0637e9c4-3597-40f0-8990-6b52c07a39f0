const app = getApp()

Page({
  data: {
    companyInfo: null,
    products: [],
    fromCardDetail: false,
    cardId: null
  },

  onLoad(options) {
    // 从用户信息获取企业信息
    if (app.globalData.userInfo && app.globalData.userInfo.companyCode) {
      this.getCompanyInfo()
    }
    
    // 检查是否有从URL传来的参数
    if (options.cardId && options.fromCardDetail) {
      this.setData({
        fromCardDetail: true,
        cardId: options.cardId
      })
      return
    }
    
    // 检查eventChannel是否存在数据传递
    const eventChannel = this.getOpenerEventChannel()
    if (eventChannel) {
      try {
        eventChannel.on('acceptDataFromCardDetail', data => {
          if (data && data.fromCardDetail) {
            this.setData({
              fromCardDetail: true,
              cardId: data.cardId
            })
          }
        })
      } catch (err) {
        console.log('获取事件通道数据失败', err)
      }
    }
    
    // 从本地存储获取名片ID
    const currentCardId = wx.getStorageSync('current_card_id')
    if (currentCardId) {
      this.setData({
        fromCardDetail: true,
        cardId: currentCardId
      })
    }
  },

  getCompanyInfo() {
    const company = require('../../utils/company.js')
    company.getCompanyInfo().then(res => {
      if (res.code === 0 && res.data) {
        this.setData({
          companyInfo: res.data
        })
      }
    }).catch(err => {
      console.error('获取企业信息失败', err)
    })
  },

  contactUs() {
    wx.showModal({
      title: '联系我们',
      content: '如需了解更多产品信息，请联系客服电话：400-123-4567',
      confirmText: '复制号码',
      success(res) {
        if (res.confirm) {
          wx.setClipboardData({
            data: '400-123-4567',
            success() {
              wx.showToast({
                title: '已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  }
}) 