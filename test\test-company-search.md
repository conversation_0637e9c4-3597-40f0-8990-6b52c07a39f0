# 企业搜索功能测试

## 测试目标
验证企业名称自动完成功能在各种场景下的表现，包括搜索、选择、提交等。

## 测试环境准备

### 1. 数据库测试数据
确保数据库中有以下测试企业数据：

```sql
-- 插入测试企业数据
INSERT INTO companies (company_code, name, enterprise_name, products_name) VALUES
('CO202412010001', '上饶市数字技术应用协会', '上饶市数字技术应用协会AI名片', '上饶市数字技术应用协会产品中心'),
('CO202412010002', '上海科技有限公司', '上海科技有限公司AI名片', '上海科技有限公司产品中心'),
('CO202412010003', '上饶智能科技', '上饶智能科技AI名片', '上饶智能科技产品中心'),
('CO202412010004', '北京上市公司', '北京上市公司AI名片', '北京上市公司产品中心'),
('CO202412010005', '深圳上游科技', '深圳上游科技AI名片', '深圳上游科技产品中心');
```

### 2. API测试
使用以下命令测试搜索API：

```bash
# 测试搜索"上"关键词
curl -X GET "https://zl.sdtaa.com/api/v1/company/search?keyword=上"

# 测试搜索"上饶"关键词
curl -X GET "https://zl.sdtaa.com/api/v1/company/search?keyword=上饶"

# 测试空关键词
curl -X GET "https://zl.sdtaa.com/api/v1/company/search?keyword="
```

## 已知问题和修复

### 问题1: API路径重复
**问题描述**: 请求URL变成了 `/api/v1/api/company/search`，出现了重复的 `/api`
**原因**: 自动完成组件的默认searchUrl设置为 `/api/company/search`，与基础URL `/api/v1` 拼接时产生重复
**修复方案**: 将searchUrl改为 `/company/search`

### 问题2: 404错误
**错误信息**: `接口 /api/v1/api/company/search?keyword=%E4%B8%8A 不存在`
**状态**: 已修复，通过调整API路径解决

## 前端功能测试

### 1. 创建名片页面测试
- [ ] 打开创建名片页面
- [ ] 在公司输入框中输入"上"
- [ ] 验证是否显示下拉建议列表
- [ ] 验证建议列表中包含"上饶市数字技术应用协会"等企业
- [ ] 点击选择一个企业
- [ ] 验证输入框是否正确填充选中的企业名称
- [ ] 提交表单，验证名片创建是否成功

### 2. 编辑名片页面测试
- [ ] 打开编辑名片页面
- [ ] 清空公司输入框
- [ ] 在公司输入框中输入"上饶"
- [ ] 验证是否显示相关的下拉建议
- [ ] 选择一个企业
- [ ] 保存修改，验证是否成功

### 3. 边界情况测试
- [ ] 输入不存在的企业名称，验证"未找到匹配的企业"提示
- [ ] 输入单个字符，验证搜索是否正常触发
- [ ] 快速输入多个字符，验证防抖功能是否正常
- [ ] 点击输入框外部，验证下拉列表是否正确隐藏
- [ ] 在有搜索结果时重新获得焦点，验证下拉列表是否重新显示

### 4. 用户体验测试
- [ ] 验证搜索加载指示器是否正常显示
- [ ] 验证清空按钮是否正常工作
- [ ] 验证下拉列表的滚动是否流畅
- [ ] 验证选中项的高亮效果
- [ ] 验证在移动设备上的触摸体验

## 性能测试
- [ ] 测试搜索响应时间是否在可接受范围内（< 500ms）
- [ ] 测试连续快速输入时的性能表现
- [ ] 测试大量搜索结果时的渲染性能

## 兼容性测试
- [ ] 在不同的微信版本中测试
- [ ] 在不同的手机型号上测试
- [ ] 测试网络较慢时的表现

## 错误处理测试
- [ ] 测试网络断开时的错误处理
- [ ] 测试服务器返回错误时的处理
- [ ] 测试API超时的处理

## 预期结果
1. 用户输入企业名称关键词时，能够实时显示匹配的企业列表
2. 用户可以通过点击选择企业，自动填充到输入框中
3. 搜索功能具有良好的性能和用户体验
4. 错误情况下有适当的提示和处理

## 测试记录
请在测试过程中记录发现的问题和改进建议。
