.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 200rpx;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.create-btn {
  background-color: #1aad19;
  color: white;
  font-size: 30rpx;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  border: none;
}

/* 名片列表 */
.card-list {
  margin-top: 20rpx;
}

.card-item {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.card-selected {
  border: 2rpx solid #1aad19;
  background-color: rgba(26, 173, 25, 0.05);
}

/* 名片内容区域 */
.card-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
  position: relative;
}

.card-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  border: 1rpx solid #eee;
}

.card-avatar image {
  width: 100%;
  height: 100%;
}

.card-info {
  flex: 1;
}

.card-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.card-company {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.card-position {
  font-size: 26rpx;
  color: #999;
}

.card-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20rpx;
}

.status-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}

.status-text {
  font-size: 24rpx;
  color: #1aad19;
}

/* 操作按钮区域 */
.card-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  font-size: 26rpx;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.edit-btn {
  color: #3E7FFF;
  border-right: 1rpx solid #f0f0f0;
}

.delete-btn {
  color: #FF3B30;
}

/* 创建新名片按钮 */
.create-card-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  padding: 30rpx;
  margin: 40rpx 0;
  color: #666;
  font-size: 30rpx;
}

.plus-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

/* 返回按钮 */
.back-btn-wrapper {
  padding: 0 30rpx;
}

.back-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #3E7FFF, #72AAFF);
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  outline: none;
  box-shadow: none;
}

.back-btn::after {
  border: none;
  display: none;
} 