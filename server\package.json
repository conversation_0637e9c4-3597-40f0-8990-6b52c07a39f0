{"name": "zhilian-server", "version": "1.0.0", "description": "智链微信小程序后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "qiniu": "^7.10.1", "sequelize": "^6.35.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=14.0.0"}}