/**
 * 数据库配置文件
 */
require('dotenv').config();

module.exports = {
  host: process.env.DB_HOST || '**************',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'zhi_lian',
  username: process.env.DB_USER || 'zhi_lian',
  password: process.env.DB_PASSWORD || 'rD6YaJYpybweKsTn',
  dialect: 'mysql',
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  define: {
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    paranoid: false
  },
  logging: process.env.NODE_ENV === 'development' ? console.log : false
}; 