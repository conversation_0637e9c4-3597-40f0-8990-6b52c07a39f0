/**
 * 名片收藏路由
 */
const express = require('express');
const router = express.Router();
const collectionController = require('../controllers/collection.controller');
const { verifyToken } = require('../middlewares/auth.middleware');

// 收藏名片
router.post('/', verifyToken, collectionController.collectCard);

// 获取收藏列表
router.get('/', verifyToken, collectionController.getCollections);

// 检查名片是否已收藏
router.get('/check', verifyToken, collectionController.checkCollection);

// 删除收藏
router.delete('/:id', verifyToken, collectionController.deleteCollection);

module.exports = router; 