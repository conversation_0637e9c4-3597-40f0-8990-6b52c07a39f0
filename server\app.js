/**
 * 智链微信小程序后端服务主文件
 */
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const rateLimit = require('express-rate-limit');
const routes = require('./routes');
const db = require('./models');
const { notFound, errorHandler } = require('./middlewares/error.middleware');
const config = require('./config/app.config');

// 创建Express应用
const app = express();

// 信任代理设置（用于反向代理环境，如Nginx）
app.set('trust proxy', true);

// 配置基本中间件
app.use(helmet()); // 安全头
app.use(cors()); // 跨域
app.use(express.json()); // JSON解析
app.use(express.urlencoded({ extended: true })); // URL编码解析
app.use(morgan('dev')); // 日志

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 请求频率限制
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP 15分钟内最多1000个请求
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    code: 429,
    message: '请求过于频繁，请稍后再试',
    data: null
  }
});
app.use(apiLimiter);

// API路由
app.use(config.apiPrefix, routes);

// 404处理
app.use(notFound);

// 错误处理
app.use(errorHandler);

// 启动服务器
const PORT = config.port;
app.listen(PORT, async () => {
  console.log(`服务器运行在端口 ${PORT}`);
  
  try {
    // 测试数据库连接
    await db.sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 同步数据库模型（开发环境可用，生产环境请谨慎）
    if (config.env === 'development') {
      await db.sequelize.sync({ alter: true });
      console.log('数据库模型同步完成');
    }
  } catch (error) {
    console.error('数据库连接失败:', error);
  }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

module.exports = app; 