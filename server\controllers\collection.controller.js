/**
 * 名片收藏控制器
 */
const db = require('../models');
const { generateResponse, getPagination, getPaginationData } = require('../utils/common');
const { ApiError } = require('../middlewares/error.middleware');
const { Op } = require('sequelize');

const CardCollection = db.CardCollection;
const BusinessCard = db.BusinessCard;

/**
 * 收藏名片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.collectCard = async (req, res, next) => {
  try {
    const { cardId } = req.body;
    
    if (!cardId) {
      throw new ApiError(400, '缺少必要参数：cardId');
    }

    // 检查名片是否存在
    const businessCard = await BusinessCard.findByPk(cardId);
    if (!businessCard) {
      throw new ApiError(404, '名片不存在');
    }

    // 检查是否是自己的名片
    // 使用双等号比较，允许类型转换
    if (businessCard.user_id == req.userId) {
      throw new ApiError(400, '不能收藏自己的名片');
    }

    // 检查是否已收藏该名片
    const existingCollection = await CardCollection.findOne({
      where: {
        user_id: req.userId,
        card_id: cardId
      }
    });

    if (existingCollection) {
      // 已经收藏过了，返回成功而不是错误
      return res.json(generateResponse(0, 'success', existingCollection));
    }

    // 创建收藏记录
    const collection = await CardCollection.create({
      user_id: req.userId,
      card_id: cardId
    });

    return res.json(generateResponse(0, 'success', collection));
  } catch (error) {
    next(error);
  }
};

/**
 * 获取用户名片夹列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.getCollections = async (req, res, next) => {
  try {
    const { page, pageSize, keyword } = req.query;
    const { offset, limit } = getPagination({ page, pageSize });

    // 构建查询条件
    const whereCondition = {
      user_id: req.userId
    };

    // 如果有关键字，增加名片内容搜索条件
    let cardWhereCondition = {};
    if (keyword) {
      cardWhereCondition = {
        [Op.or]: [
          { name: { [Op.like]: `%${keyword}%` } },
          { company: { [Op.like]: `%${keyword}%` } },
          { position: { [Op.like]: `%${keyword}%` } }
        ]
      };
    }

    // 查询并分页
    const collections = await CardCollection.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: BusinessCard,
          as: 'businessCard',
          where: cardWhereCondition,
          attributes: ['id', 'avatar', 'name', 'company', 'position', 'company_code']
        }
      ],
      offset,
      limit,
      order: [['created_at', 'DESC']]
    });

    // 格式化数据
    const formattedData = collections.rows.map(item => {
      const collection = item.toJSON();
      return {
        id: collection.businessCard.id,
        collectionId: collection.id,
        avatar: collection.businessCard.avatar,
        name: collection.businessCard.name,
        company: collection.businessCard.company,
        position: collection.businessCard.position,
        companyCode: collection.businessCard.company_code,
        collectedAt: collection.created_at
      };
    });

    // 返回分页结果
    const result = {
      total: collections.count,
      page: parseInt(page) || 1,
      pageSize: parseInt(pageSize) || 20,
      list: formattedData
    };

    return res.json(generateResponse(0, 'success', result));
  } catch (error) {
    next(error);
  }
};

/**
 * 删除收藏的名片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.deleteCollection = async (req, res, next) => {
  try {
    const collectionId = req.params.id;
    
    if (!collectionId) {
      throw new ApiError(400, '缺少必要参数：id');
    }

    // 查找收藏记录
    const collection = await CardCollection.findByPk(collectionId);

    if (!collection) {
      throw new ApiError(404, '收藏记录不存在');
    }

    // 验证是否为用户自己的收藏
    if (collection.user_id !== req.userId) {
      throw new ApiError(403, '无权限删除此收藏');
    }

    // 删除收藏记录
    await collection.destroy();

    return res.json(generateResponse(0, 'success', null));
  } catch (error) {
    next(error);
  }
};

/**
 * 检查名片是否已收藏
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.checkCollection = async (req, res, next) => {
  try {
    const { cardId } = req.query;
    
    if (!cardId) {
      throw new ApiError(400, '缺少必要参数：cardId');
    }

    // 检查是否已收藏该名片
    const existingCollection = await CardCollection.findOne({
      where: {
        user_id: req.userId,
        card_id: cardId
      }
    });

    return res.json(generateResponse(0, 'success', { 
      isCollected: !!existingCollection 
    }));
  } catch (error) {
    next(error);
  }
}; 