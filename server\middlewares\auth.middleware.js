/**
 * 认证中间件
 */
const jwt = require('jsonwebtoken');
const config = require('../config/app.config');
const db = require('../models');
const User = db.User;

/**
 * JWT令牌验证中间件
 */
exports.verifyToken = (req, res, next) => {
  // 从请求头获取token
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN格式

  if (!token) {
    return res.status(401).json({
      code: 401,
      message: '未提供认证令牌',
      data: null
    });
  }

  // 验证token
  jwt.verify(token, config.jwt.secret, (err, decoded) => {
    if (err) {
      return res.status(401).json({
        code: 401,
        message: '认证令牌无效或已过期',
        data: null
      });
    }

    // 将解码后的用户信息存储到请求对象中
    // 确保userId是整数类型，与数据库中的类型一致
    req.userId = parseInt(decoded.id);
    req.openid = decoded.openid;
    next();
  });
};

/**
 * 可选的JWT令牌验证中间件
 * 当token存在时验证并设置用户ID，不存在或无效时也允许请求继续
 */
exports.optionalVerifyToken = (req, res, next) => {
  // 从请求头获取token
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN格式

  if (!token) {
    // 没有token，继续处理请求但不设置用户ID
    return next();
  }

  // 验证token
  jwt.verify(token, config.jwt.secret, (err, decoded) => {
    if (!err) {
      // token有效，设置用户ID
      // 确保userId是整数类型，与数据库中的类型一致
      req.userId = parseInt(decoded.id);
      req.openid = decoded.openid;
    }
    // 即使token无效也继续处理
    next();
  });
};

/**
 * 检查用户是否存在中间件
 */
exports.checkUserExists = async (req, res, next) => {
  try {
    const user = await User.findOne({
      where: { id: req.userId }
    });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 将用户对象存储到请求中
    req.user = user;
    next();
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
}; 