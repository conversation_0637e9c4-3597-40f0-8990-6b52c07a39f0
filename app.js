// app.js
App({
  // 登录锁，防止多次并发登录
  isLoggingIn: false,
  // 登录请求队列
  loginCallbacks: [],

  onLaunch() {
    // 获取本地存储的登录信息
    const token = wx.getStorageSync('token') || '';
    const userInfo = wx.getStorageSync('userInfo') || null;
    
    if (token) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
      // 检查微信session和后端token有效性
      this.checkSession();
    } else {
      // 没有token时确保状态清理干净
      this.clearLoginState();
    }
    
    // 初始化TabBar控制状态
    this.initTabBarControl();
    
    // 监听页面显示事件，确保TabBar状态在页面切换时正确
    wx.onAppShow(() => {
      this.updateTabBarStatus();
    });
  },
  
  // 初始化TabBar控制状态
  initTabBarControl() {
    // 创建全局TabBar控制对象 - 修改为始终显示TabBar
    this.globalData.tabBarControl = {
      show: true, // 默认显示TabBar，不再依赖用户登录状态
      needCheckStatus: true // 允许自动检查
    };
  },
  
  // 更新TabBar状态
  updateTabBarStatus() {
    // 默认应该显示TabBar，除了登录页面外
    const shouldShowTabBar = true;
    
    // 更新全局控制状态
    if (this.globalData.tabBarControl) {
      // 如果当前状态与应该的状态不同，需要更新
      if (this.globalData.tabBarControl.show !== shouldShowTabBar) {
        this.globalData.tabBarControl.show = shouldShowTabBar;
        this.globalData.tabBarControl.needCheckStatus = true;
        
        console.log(`全局TabBar控制状态更新: ${shouldShowTabBar ? '显示' : '隐藏'}`);
        
        // 尝试更新当前页面的TabBar
        this.updateCurrentPageTabBar();
      }
    } else {
      this.initTabBarControl();
      
      // 初始化后也尝试更新当前页面TabBar
      this.updateCurrentPageTabBar();
    }
  },
  
  // 更新当前页面的TabBar状态
  updateCurrentPageTabBar() {
    try {
      const pages = getCurrentPages();
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        if (currentPage && typeof currentPage.getTabBar === 'function') {
          const tabBar = currentPage.getTabBar();
          if (tabBar) {
            // 让TabBar与全局状态保持一致
            const show = this.globalData.tabBarControl && this.globalData.tabBarControl.show;
            tabBar.setData({ show: show });
            
            // 如果有专门的方法，也调用它
            if (typeof tabBar.setTabBarShow === 'function') {
              tabBar.setTabBarShow(show, this.globalData.tabBarControl.needCheckStatus);
            }
          }
        }
      }
    } catch (e) {
      console.error('更新当前页面TabBar失败:', e);
    }
  },
  
  // 检查登录是否过期
  checkSession() {
    wx.checkSession({
      success: () => {
        console.log('微信session有效');
        // session有效，但还需要验证后端token是否有效
        this.validateToken();
      },
      fail: () => {
        console.log('微信session已失效，清除登录状态');
        // session_key 已经失效，需要重新登录
        this.clearLoginState();
      }
    });
  },

  // 验证后端token是否有效
  validateToken() {
    if (!this.globalData.token) {
      return;
    }

    // 发起一个简单的验证请求
    wx.request({
      url: `${this.globalData.baseUrl}/auth/user`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${this.globalData.token}`
      },
      success: (res) => {
        if (res.statusCode === 401 || (res.data && res.data.code === 401)) {
          console.log('后端token已失效，清除登录状态');
          this.clearLoginState();
        } else {
          console.log('后端token有效');
        }
      },
      fail: (err) => {
        console.log('验证token失败:', err);
        // 网络错误不清除登录状态，让用户在使用时再处理
      }
    });
  },

  // 清除登录状态的统一方法
  clearLoginState() {
    // 清理全局数据
    this.globalData.token = '';
    this.globalData.userInfo = null;
    this.globalData.cardInfo = null;
    this.globalData.companyInfo = null;
    this.globalData.cardUpdated = false;

    // 清理本地存储
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('cardInfo');
    wx.removeStorageSync('companyInfo');

    // 更新TabBar显示状态
    this.updateTabBarStatus();
  },
  
  // 登录方法
  login(callback) {
    // 如果已经在登录中，将回调加入队列
    if (this.isLoggingIn) {
      this.loginCallbacks.push(callback);
      return;
    }
    
    // 设置登录状态锁
    this.isLoggingIn = true;
    
    wx.login({
      success: res => {
        if (res.code) {
          // 调用后端登录接口，获取token和用户信息
          wx.request({
            url: `${this.globalData.baseUrl}/auth/login`,
            method: 'POST',
            data: {
              code: res.code
            },
            success: response => {
              const { code, message, data } = response.data;
              
              // 解除登录锁
              this.isLoggingIn = false;
              
              let success = false;
              
              if (code === 0 && data) {
                // 保存登录信息
                this.globalData.token = data.token;
                this.globalData.userInfo = {
                  userId: data.userId,
                  openid: data.openid,
                  companyCode: data.companyCode,
                  hasCardInfo: data.hasCardInfo
                };
                
                // 存储到本地
                wx.setStorageSync('token', data.token);
                wx.setStorageSync('userInfo', this.globalData.userInfo);
                
                // 更新TabBar显示状态
                this.updateTabBarStatus();
                
                success = true;
              } else {
                wx.showToast({
                  title: message || '登录失败',
                  icon: 'none'
                });
                
                // 更新TabBar显示状态
                this.updateTabBarStatus();
              }
              
              // 调用当前回调
              if (typeof callback === 'function') {
                callback(success);
              }
              
              // 处理排队的回调
              this.loginCallbacks.forEach(cb => {
                if (typeof cb === 'function') {
                  cb(success);
                }
              });
              
              // 清空回调队列
              this.loginCallbacks = [];
            },
            fail: err => {
              console.error('登录请求失败', err);
              
              // 解除登录锁
              this.isLoggingIn = false;
              
              // 处理429错误 (Too Many Requests)
              if (err.statusCode === 429) {
                wx.showToast({
                  title: '登录请求过多，请稍后再试',
                  icon: 'none',
                  duration: 2000
                });
              } else {
                wx.showToast({
                  title: '网络错误，请重试',
                  icon: 'none'
                });
              }
              
              // 更新TabBar显示状态
              this.updateTabBarStatus();
              
              // 调用当前回调
              if (typeof callback === 'function') {
                callback(false);
              }
              
              // 处理排队的回调
              this.loginCallbacks.forEach(cb => {
                if (typeof cb === 'function') {
                  cb(false);
                }
              });
              
              // 清空回调队列
              this.loginCallbacks = [];
            }
          });
        } else {
          // 解除登录锁
          this.isLoggingIn = false;
          
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          });
          
          // 更新TabBar显示状态
          this.updateTabBarStatus();
          
          // 调用当前回调
          if (typeof callback === 'function') {
            callback(false);
          }
          
          // 处理排队的回调
          this.loginCallbacks.forEach(cb => {
            if (typeof cb === 'function') {
              cb(false);
            }
          });
          
          // 清空回调队列
          this.loginCallbacks = [];
        }
      },
      fail: () => {
        // 解除登录锁
        this.isLoggingIn = false;
        
        wx.showToast({
          title: '获取登录凭证失败',
          icon: 'none'
        });
        
        // 更新TabBar显示状态
        this.updateTabBarStatus();
        
        // 调用当前回调
        if (typeof callback === 'function') {
          callback(false);
        }
        
        // 处理排队的回调
        this.loginCallbacks.forEach(cb => {
          if (typeof cb === 'function') {
            cb(false);
          }
        });
        
        // 清空回调队列
        this.loginCallbacks = [];
      }
    });
  },

  globalData: {
    baseUrl: 'https://zl.sdtaa.com/api/v1', // API基础URL
    userInfo: null,
    token: '',
    cardInfo: null, // 用户名片信息
    companyInfo: null, // 用户企业信息
    cardUpdated: false, // 标记名片是否被更新
    tabBarControl: null // TabBar控制状态，将在初始化时设置
  }
})
