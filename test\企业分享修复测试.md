# 企业分享修复测试

## 测试场景

### 场景1：从个人名片页面进入企业页面后分享
1. 用户在个人名片页面
2. 点击"企业"按钮进入企业页面
3. 点击右上角三个点分享企业

**预期结果：**
- 分享链接应该包含当前用户的cardId
- 别人点击分享链接后，能看到"返回名片"按钮
- 点击"返回名片"能正确返回到分享者的名片页面

### 场景2：从分享链接进入企业页面
1. 别人点击企业分享链接
2. 进入企业页面，显示导航栏
3. 测试导航栏各个功能

**预期结果：**
- 显示cover-view导航栏
- "返回名片"按钮正常工作
- "产品中心"按钮正常工作
- "分享"按钮正常工作

## 修改内容

### 1. 个人页面跳转企业页面
- 修改`goToEnterprise()`方法，传递cardId和fromPersonal参数
- 路径：`/pages/enterprise/index?cardId=${cardId}&fromPersonal=true`

### 2. 企业页面处理个人页面进入
- 在onLoad中添加fromPersonal处理逻辑
- 设置cardId和fromPersonal标记

### 3. 企业页面分享逻辑
- 修改onShareAppMessage方法
- 如果没有cardId，从全局数据中获取
- 确保分享时携带正确的cardId

## 关键代码变更

```javascript
// pages/personal/index.js - goToEnterprise方法
goToEnterprise() {
  let url = '/pages/enterprise/index';
  if (this.data.cardInfo && this.data.cardInfo.id) {
    url += `?cardId=${this.data.cardInfo.id}&fromPersonal=true`;
  }
  wx.navigateTo({ url: url })
}

// pages/enterprise/index.js - onLoad方法
if (options && options.fromPersonal === 'true') {
  console.log("从个人页面进入企业页面");
  if (options.cardId) {
    this.setData({
      cardId: options.cardId,
      fromPersonal: true
    });
  }
}

// pages/enterprise/index.js - onShareAppMessage方法
let cardId = this.data.cardId;
if (!cardId && app.globalData.cardInfo && app.globalData.cardInfo.id) {
  cardId = app.globalData.cardInfo.id;
}
```

## 测试步骤

1. 启动小程序，登录并创建名片
2. 在个人页面点击"企业"按钮
3. 在企业页面点击右上角分享
4. 复制分享链接，在另一个设备或清除数据后测试
5. 验证导航栏功能是否正常

## 预期修复的问题

- ✅ 从个人页面进入企业页面后分享，能正确携带cardId
- ✅ 分享后的页面能正确显示"返回名片"按钮
- ✅ 点击"返回名片"能正确跳转到分享者的名片页面
- ✅ 导航栏的其他功能（产品中心、分享）正常工作
