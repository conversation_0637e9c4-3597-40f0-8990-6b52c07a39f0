<view class="card-list-container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{empty}}">
    <image class="empty-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/empty-folder.png" mode="aspectFit"></image>
    <text class="empty-text">您的名片夹还是空的~\n收藏名片后会显示在这里</text>
  </view>
  
  <!-- 名片列表 -->
  <view class="card-list" wx:else>
    <view class="card-item" wx:for="{{cards}}" wx:key="id" bindtap="onCardTap" data-id="{{item.id}}">
      <view class="card-content">
        <image class="avatar" src="{{item.avatar || defaultAvatar}}" mode="aspectFill"></image>
        <view class="card-info">
          <view class="name-row">
            <text class="name">{{item.name}}</text>
            <text class="industry" wx:if="{{item.industry}}">{{item.industry}}</text>
          </view>
          <view class="position" wx:if="{{item.position}}">{{item.position}}</view>
          <view class="company">{{item.company}}</view>
          <view class="contact" wx:if="{{item.mobile}}">
            <view class="contact-icon phone-icon"></view>
            <text>{{item.mobile}}</text>
          </view>
        </view>
      </view>
      
      <view class="add-time" wx:if="{{item.collectedAt}}">收藏于: {{item.collectedAt}}</view>
      
      <view class="card-delete" wx:if="{{item.collectionId}}" catchtap="onDeleteCard" data-id="{{item.id}}" data-collection-id="{{item.collectionId}}">
        <view class="delete-icon"></view>
      </view>
    </view>
  </view>
</view> 