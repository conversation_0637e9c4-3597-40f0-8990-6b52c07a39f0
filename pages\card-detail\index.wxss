.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 150rpx; /* 增加底部间距，为底部按钮留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3E7FFF;
  margin: 0 10rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

/* 名片样式 - 与business-card组件保持一致 */
.business-card {
  width: 100%;
  margin-bottom: 30rpx;
}

.card-body {
  background: linear-gradient(135deg, #3E7FFF, #72AAFF);
  padding: 24rpx;
  position: relative;
  overflow: hidden;
  border-radius: 20rpx;
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 卡片背景装饰 */
.card-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="%234B8BFF"/><rect x="0" y="0" width="50" height="50" fill="%233E7FFF"/><rect x="50" y="50" width="50" height="50" fill="%233E7FFF"/></svg>');
  background-size: 20rpx 20rpx;
  opacity: 0.1;
  z-index: 0;
}

/* 基本信息 */
.card-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  position: relative;
  z-index: 1;
  align-items: flex-start;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.2);
}

.basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-right: 16rpx;
}

.name {
  font-size: 34rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 6rpx;
}

.company {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4rpx;
}

.position {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 联系方式 */
.contact-section {
  margin-top: 16rpx;
  position: relative;
  z-index: 1;
}

.contact-item {
  display: flex;
  padding: 6rpx 0;
  align-items: center;
  margin-bottom: 4rpx;
}

.contact-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
  filter: brightness(0) invert(1);
}

.contact-value {
  font-size: 22rpx;
  color: #ffffff;
  word-break: break-all;
}

/* 操作按钮 */
.action-bar {
  display: flex;
  margin-bottom: 30rpx;
  padding: 0;
  justify-content: center;
  gap: 20rpx; /* 减少按钮间距以适配三个按钮 */
}

.button-wrapper {
  flex: 1; /* 使用flex布局让按钮平均分配空间 */
  max-width: 30%; /* 限制最大宽度以适配三个按钮 */
  box-sizing: border-box;
}

.action-button {
  width: 100% !important; /* 强制宽度100% */
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  background-color: #3E7FFF;
  color: #fff;
  padding: 0 !important; /* 强制清除内边距 */
  margin: 0 !important; /* 强制清除外边距 */
  border: none !important; /* 强制清除边框 */
  line-height: 1 !important; /* 强制行高 */
  box-sizing: border-box;
  min-width: auto !important; /* 清除最小宽度限制 */
}

.action-button.disabled {
  background-color: #e0e0e0;
  color: #999;
}

.share-button {
  background-color: #FF6B57;
}

.ai-avatar-button {
  background-color: #9C27B0; /* 紫色背景，区别于其他按钮 */
}

/* 详情信息区域 */
.detail-section {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
  border-left: 6rpx solid #3E7FFF;
  height: 30rpx;
  line-height: 30rpx;
}

/* 水平布局的信息项 */
.info-item-horizontal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item-horizontal:last-child {
  border-bottom: none;
}

.info-left {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.info-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.info-label {
  font-size: 26rpx;
  color: #333;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mobile-icon {
  background-image: url('/images/common/phone.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.wechat-icon {
  background-image: url('/images/common/wechat.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.email-icon {
  background-image: url('/images/common/email.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.phone-icon {
  background-image: url('/images/common/telephone.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.website-icon {
  background-image: url('/images/common/website.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.address-icon {
  background-image: url('/images/common/address.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.info-action {
  font-size: 24rpx;
  color: #3E7FFF;
  margin-left: 20rpx;
  padding: 8rpx 20rpx;
  background-color: rgba(62, 127, 255, 0.1);
  border-radius: 30rpx;
}

/* 业务简介 */
.introduction {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* Web-view样式 */
.webview-container {
  width: 100%;
  height: calc(100vh - 116rpx - env(safe-area-inset-bottom)); /* 减去底部导航栏高度 */
}

/* 修改WebView样式 */
web-view {
  width: 100%;
  height: 100%;
}

/* 企业和产品中心模块 */
.module-section {
  margin-bottom: 10rpx; /* 增加底部模块的底部间距 */
}

.module-card {
  display: flex;
  flex-direction: row;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 25rpx;
  overflow: hidden;
  padding: 20rpx;
  position: relative;
  box-sizing: border-box;
}

/* 左侧封面图片区域 - 5:4比例 */
.module-cover {
  width: 200rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.module-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.module-card:active .module-cover-image {
  transform: scale(1.05);
}

/* 右侧内容区域 */
.module-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 160rpx;
  justify-content: space-between;
}

/* 信息容器 */
.module-info {
  flex: 1;
}

.module-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
}

.module-subtitle {
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.module-action {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #3E7FFF;
  font-weight: 500;
  align-self: flex-end;
  margin-top: auto;
}

.arrow-text {
  margin-left: 6rpx;
  font-size: 24rpx;
  color: #3E7FFF;
  font-weight: 500;
}



/* 底部创建/回到我的名片按钮 */
.create-card-btn {
  position: fixed;
  bottom: 40rpx; /* 移到屏幕最底部 */
  left: 50%;
  transform: translateX(-50%);
  background-color: #3E7FFF;
  color: white;
  font-size: 32rpx; /* 增大字体 */
  padding: 24rpx 50rpx; /* 增大内边距 */
  border-radius: 44rpx; /* 调整圆角 */
  box-shadow: 0 6rpx 12rpx rgba(62, 127, 255, 0.3); /* 增强阴影效果 */
  z-index: 999;
  text-align: center;
}