.container {
  padding: 16rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 130rpx;
}

/* 页面标题 */
.page-header {
  margin-bottom: 24rpx;
  padding: 20rpx 10rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #666;
}

/* 头像上传区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
}

.avatar-wrapper {
  position: relative;
  width: 130rpx;
  height: 130rpx;
  border-radius: 50%;
  margin-bottom: 12rpx;
  overflow: hidden;
  background-color: #e0e0e0;
}

.avatar {
  width: 100%;
  height: 100%;
}

.avatar-upload-icon {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 36rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-upload-icon image {
  width: 22rpx;
  height: 22rpx;
}

.wechat-avatar-btn {
  font-size: 24rpx;
  color: #3E7FFF;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  outline: none;
  box-shadow: none;
  text-align: center;
}

.wechat-avatar-btn::after {
  border: none;
  display: none;
}

/* 表单样式 */
.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 24rpx;
}

.form-section {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.form-item {
  margin-bottom: 16rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.required {
  color: #ff4d4f;
}

.form-input {
  height: 64rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 24rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
}

.picker {
  height: 64rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 24rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.placeholder {
  color: #999;
}

.form-textarea {
  width: 100%;
  height: 180rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  font-size: 24rpx;
  color: #333;
  box-sizing: border-box;
}

.textarea-counter {
  text-align: right;
  font-size: 20rpx;
  color: #999;
  margin-top: 6rpx;
}

/* 更多选项 */
.more-options {
  margin-top: 16rpx;
}

.more-options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  font-size: 24rpx;
  color: #3E7FFF;
}

.arrow {
  width: 22rpx;
  height: 22rpx;
  transition: all 0.3s;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.arrow-down {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233E7FFF"><path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"></path></svg>');
}

.arrow-up {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233E7FFF"><path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"></path></svg>');
}

.more-options-content {
  padding-top: 8rpx;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮组 */
.button-group {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.cancel-btn {
  width: 240rpx;
  height: 80rpx;
  background-color: #e9e9e9;
  color: #666;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin-right: 0;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-btn {
  width: 240rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #3E7FFF, #72AAFF);
  color: white;
  font-size: 28rpx;
  border-radius: 40rpx;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-btn-full {
  width: 500rpx;
  margin-right: 0;
} 