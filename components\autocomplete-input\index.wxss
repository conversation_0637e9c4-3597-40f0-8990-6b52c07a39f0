/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: transparent;
}

.autocomplete-container {
  position: relative;
  width: 100%;
  z-index: 1000;
}

.form-label {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.required {
  color: #ff4d4f;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  height: 64rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 16rpx;
  padding-right: 80rpx; /* 为清空按钮和加载指示器留空间 */
  font-size: 24rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
  border: 1rpx solid transparent;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: #3E7FFF;
  background-color: #fff;
}

.clear-btn {
  position: absolute;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
  font-size: 32rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.loading-indicator {
  position: absolute;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #3E7FFF;
  border-top: 2rpx solid transparent;
  border-radius: 50%;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1001;
  margin-top: 4rpx;
  overflow: hidden;
}

.dropdown-scroll {
  width: 100%;
}

.dropdown-item {
  padding: 24rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:active {
  background-color: #f5f7fa;
}

.dropdown-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
}

.no-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1001;
  margin-top: 4rpx;
  padding: 24rpx 16rpx;
  text-align: center;
}

.no-results-text {
  font-size: 24rpx;
  color: #999;
}
