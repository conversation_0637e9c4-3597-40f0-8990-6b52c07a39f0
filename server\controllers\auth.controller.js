/**
 * 认证控制器
 */
const jwt = require('jsonwebtoken');
const db = require('../models');
const weappUtil = require('../utils/weapp');
const { generateResponse } = require('../utils/common');
const config = require('../config/app.config');
const { ApiError } = require('../middlewares/error.middleware');

const User = db.User;
const BusinessCard = db.BusinessCard;

/**
 * 微信小程序登录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.login = async (req, res, next) => {
  try {
    const { code } = req.body;

    if (!code) {
      throw new ApiError(400, '缺少必要参数：code');
    }

    // 调用微信接口获取openid和session_key
    const wxLoginResult = await weappUtil.code2Session(code);
    const { openid, session_key, unionid } = wxLoginResult;

    // 查找或创建用户
    let user = await User.findOne({ where: { openid } });
    
    if (!user) {
      // 用户不存在，创建新用户
      user = await User.create({
        openid,
        unionid,
        session_key
      });
    } else {
      // 用户存在，更新session_key
      user.session_key = session_key;
      if (unionid) {
        user.unionid = unionid;
      }
      await user.save();
    }

    // 检查用户是否有名片信息
    const businessCard = await BusinessCard.findOne({
      where: { user_id: user.id }
    });

    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, openid: user.openid },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    // 返回用户登录信息
    return res.json(generateResponse(0, 'success', {
      token,
      userId: user.id,
      openid: user.openid,
      companyCode: user.company_code,
      aiAvatarUrl: user.ai_avatar_url,
      hasCardInfo: !!businessCard
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 获取当前登录用户信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.getUserInfo = async (req, res, next) => {
  try {
    const user = await User.findByPk(req.userId);
    
    if (!user) {
      throw new ApiError(404, '用户不存在');
    }

    // 检查用户是否有名片信息
    const businessCard = await BusinessCard.findOne({
      where: { user_id: user.id }
    });

    return res.json(generateResponse(0, 'success', {
      userId: user.id,
      openid: user.openid,
      companyCode: user.company_code,
      aiAvatarUrl: user.ai_avatar_url,
      hasCardInfo: !!businessCard
    }));
  } catch (error) {
    next(error);
  }
}; 