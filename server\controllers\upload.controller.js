/**
 * 文件上传控制器
 */
const path = require('path');
const fs = require('fs');
const { generateResponse } = require('../utils/common');
const { ApiError } = require('../middlewares/error.middleware');
const config = require('../config/app.config');
const { uploadToQiniu } = require('../utils/qiniu');

/**
 * 上传头像
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.uploadAvatar = async (req, res, next) => {
  try {
    console.log('开始处理头像上传请求');
    
    if (!req.file) {
      console.error('未接收到上传文件');
      throw new ApiError(400, '未上传文件');
    }
    
    console.log(`接收到文件: ${req.file.originalname}, 大小: ${req.file.size}字节, 路径: ${req.file.path}`);

    // 确认七牛云配置是否完整
    if (!config.qiniu.accessKey || !config.qiniu.secretKey || !config.qiniu.bucket) {
      console.error('七牛云配置不完整', {
        accessKey: !!config.qiniu.accessKey,
        secretKey: !!config.qiniu.secretKey,
        bucket: !!config.qiniu.bucket
      });
      throw new ApiError(500, '服务器配置错误，请联系管理员');
    }

    // 生成文件名
    const userId = req.userId;
    const timestamp = Date.now();
    const ext = path.extname(req.file.originalname);
    const key = `${userId}_${timestamp}${ext}`; // 简化文件名，因为路径前缀已经包含了目录结构
    
    console.log(`生成的文件名: ${key}, 完整路径: ${config.qiniu.pathPrefix}/${key}`);

    // 读取文件内容
    let fileBuffer;
    try {
      fileBuffer = fs.readFileSync(req.file.path);
      console.log(`成功读取文件，大小: ${fileBuffer.length}字节`);
    } catch (err) {
      console.error('读取上传文件失败', err);
      throw new ApiError(500, '读取上传文件失败');
    }

    // 上传到七牛云
    try {
      console.log('开始上传到七牛云');
      const result = await uploadToQiniu(fileBuffer, key);
      console.log('七牛云上传成功', result);

      // 删除临时文件
      fs.unlinkSync(req.file.path);
      console.log('临时文件已删除');

      return res.json(generateResponse(0, 'success', {
        url: result.url
      }));
    } catch (error) {
      console.error('上传到七牛云失败', error);
      throw new ApiError(500, '头像上传到云存储失败');
    }
  } catch (error) {
    console.error('头像上传过程发生错误', error);
    
    // 确保清理临时文件
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
        console.log('错误处理中：临时文件已删除');
      } catch (e) {
        console.error('删除临时文件失败', e);
      }
    }
    
    next(error);
  }
};

/**
 * 配置multer中间件
 */
exports.configureMulter = () => {
  const multer = require('multer');
  
  // 确保上传目录存在
  const uploadDir = path.join(__dirname, '..', config.upload.dir);
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  // 创建临时目录
  const tempDir = path.join(uploadDir, 'temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  // 配置存储
  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, tempDir);
    },
    filename: (req, file, cb) => {
      const timestamp = Date.now();
      const ext = path.extname(file.originalname);
      cb(null, `temp_${timestamp}${ext}`);
    }
  });

  // 文件过滤器
  const fileFilter = (req, file, cb) => {
    // 只允许上传图片
    if (!file.mimetype.startsWith('image/')) {
      return cb(new ApiError(400, '只允许上传图片文件'), false);
    }
    cb(null, true);
  };

  return multer({
    storage,
    fileFilter,
    limits: {
      fileSize: config.upload.maxFileSize // 5MB
    }
  });
};

/**
 * 获取七牛云上传凭证
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.getQiniuUploadToken = async (req, res, next) => {
  try {
    console.log('获取七牛云上传凭证');
    
    const { getDirectUploadUrl } = require('../utils/qiniu');
    const uploadInfo = getDirectUploadUrl();
    
    return res.json(generateResponse(0, 'success', uploadInfo));
  } catch (error) {
    console.error('获取上传凭证失败', error);
    next(error);
  }
}; 