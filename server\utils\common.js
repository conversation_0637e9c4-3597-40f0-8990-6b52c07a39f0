/**
 * 通用工具函数
 */

/**
 * 生成统一响应格式
 * @param {number} code - 状态码
 * @param {string} message - 状态消息
 * @param {any} data - 响应数据
 * @returns {Object} 统一格式的响应对象
 */
exports.generateResponse = (code = 0, message = 'success', data = null) => {
  return {
    code,
    message,
    data
  };
};

/**
 * 分页工具函数
 * @param {Object} queryParams - 查询参数
 * @returns {Object} 分页配置对象
 */
exports.getPagination = (queryParams) => {
  const page = parseInt(queryParams.page) || 1;
  const pageSize = parseInt(queryParams.pageSize) || 20;
  
  const offset = (page - 1) * pageSize;
  const limit = pageSize;
  
  return { offset, limit, page, pageSize };
};

/**
 * 格式化分页结果
 * @param {Object} data - 查询结果数据
 * @param {number} page - 当前页码
 * @param {number} pageSize - 每页大小
 * @returns {Object} 格式化后的分页结果
 */
exports.getPaginationData = (data, page, pageSize) => {
  return {
    total: data.count,
    page: page,
    pageSize: pageSize,
    list: data.rows
  };
};

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @returns {string} 随机字符串
 */
exports.generateRandomString = (length = 16) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * 检查对象是否为空
 * @param {Object} obj - 要检查的对象
 * @returns {boolean} 是否为空对象
 */
exports.isEmptyObject = (obj) => {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
}; 