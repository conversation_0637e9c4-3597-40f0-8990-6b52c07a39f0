/**
 * 名片控制器
 */
const db = require('../models');
const { generateResponse } = require('../utils/common');
const { ApiError } = require('../middlewares/error.middleware');
const { Sequelize } = require('sequelize');

const User = db.User;
const BusinessCard = db.BusinessCard;
const Company = db.Company;
const CardCollection = db.CardCollection;

/**
 * 获取当前用户名片信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.getUserCard = async (req, res, next) => {
  try {
    // 获取用户信息（包含AI分身URL）
    const user = await User.findByPk(req.userId);

    const businessCard = await BusinessCard.findOne({
      where: {
        user_id: req.userId,
        is_default: true
      }
    });

    if (!businessCard) {
      // 如果没有默认名片，尝试获取任意一张名片
      const anyCard = await BusinessCard.findOne({
        where: { user_id: req.userId }
      });

      if (anyCard) {
        // 将找到的名片设为默认
        await anyCard.update({ is_default: true });
        const cardData = anyCard.toJSON();
        cardData.aiAvatarUrl = user ? user.ai_avatar_url : null;
        return res.json(generateResponse(0, 'success', cardData));
      }

      return res.json(generateResponse(0, 'success', null));
    }

    const cardData = businessCard.toJSON();
    cardData.aiAvatarUrl = user ? user.ai_avatar_url : null;
    return res.json(generateResponse(0, 'success', cardData));
  } catch (error) {
    next(error);
  }
};

/**
 * 获取当前用户所有名片列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.getAllUserCards = async (req, res, next) => {
  try {
    const cards = await BusinessCard.findAll({
      where: { user_id: req.userId },
      order: [
        ['is_default', 'DESC'],
        ['updated_at', 'DESC']
      ]
    });

    return res.json(generateResponse(0, 'success', {
      list: cards,
      total: cards.length
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 创建新名片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.createNewCard = async (req, res, next) => {
  try {
    const {
      avatar, name, company, position, industry,
      mobile, wechat, email, phone, website, address, introduction
    } = req.body;

    // 验证必填字段
    if (!name || !company) {
      throw new ApiError(400, '姓名、公司为必填项');
    }

    // 根据公司名获取或创建企业编码
    const companyCode = await Company.generateCompanyCode(company);
    
    // 设置企业自定义名称和产品中心自定义名称
    const enterpriseName = `${company}AI名片`;
    const productsName = `${company}产品中心`;
    
    await Company.update(
      {
        enterprise_name: enterpriseName.slice(0, 30),
        products_name: productsName.slice(0, 30)
      },
      { where: { company_code: companyCode } }
    );

    // 检查用户是否已有名片
    const existingCards = await BusinessCard.findAll({
      where: { user_id: req.userId }
    });

    // 创建新名片，如果用户没有名片则设为默认
    const newCard = await BusinessCard.create({
      user_id: req.userId,
      avatar, name, company, position, industry,
      mobile, wechat, email, phone, website, address, introduction,
      company_code: companyCode,
      is_default: existingCards.length === 0 // 如果是第一张名片，则设为默认
    });

    // 如果是第一张名片，同步更新用户表中的company_code
    if (existingCards.length === 0) {
      await User.update(
        { company_code: companyCode },
        { where: { id: req.userId } }
      );
    }

    return res.json(generateResponse(0, 'success', newCard));
  } catch (error) {
    next(error);
  }
};

/**
 * 创建或更新用户名片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.createOrUpdateCard = async (req, res, next) => {
  try {
    const {
      avatar, name, company, position, industry,
      mobile, wechat, email, phone, website, address, introduction
    } = req.body;

    // 验证必填字段
    if (!name || !company) {
      throw new ApiError(400, '姓名、公司为必填项');
    }

    // 根据公司名获取或创建企业编码
    const companyCode = await Company.generateCompanyCode(company);
    
    // 设置企业自定义名称和产品中心自定义名称
    const enterpriseName = `${company}AI名片`;
    const productsName = `${company}产品中心`;
    
    await Company.update(
      {
        enterprise_name: enterpriseName.slice(0, 30),
        products_name: productsName.slice(0, 30)
      },
      { where: { company_code: companyCode } }
    );

    // 查找默认名片
    let businessCard = await BusinessCard.findOne({
      where: { 
        user_id: req.userId,
        is_default: true
      }
    });

    if (businessCard) {
      // 更新现有默认名片
      await businessCard.update({
        avatar, name, company, position, industry,
        mobile, wechat, email, phone, website, address, introduction,
        company_code: companyCode
      });
    } else {
      // 查找任意一张名片
      businessCard = await BusinessCard.findOne({
        where: { user_id: req.userId }
      });

      if (businessCard) {
        // 更新现有名片并设为默认
        await businessCard.update({
          avatar, name, company, position, industry,
          mobile, wechat, email, phone, website, address, introduction,
          company_code: companyCode,
          is_default: true
        });
      } else {
        // 创建新名片
        businessCard = await BusinessCard.create({
          user_id: req.userId,
          avatar, name, company, position, industry,
          mobile, wechat, email, phone, website, address, introduction,
          company_code: companyCode,
          is_default: true
        });
      }
    }

    // 同步更新用户表中的company_code
    await User.update(
      { company_code: companyCode },
      { where: { id: req.userId } }
    );

    return res.json(generateResponse(0, 'success', businessCard));
  } catch (error) {
    next(error);
  }
};

/**
 * 更新指定ID的名片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.updateCardById = async (req, res, next) => {
  try {
    const cardId = req.params.id;
    
    if (!cardId) {
      throw new ApiError(400, '缺少必要参数：id');
    }

    const {
      avatar, name, company, position, industry,
      mobile, wechat, email, phone, website, address, introduction
    } = req.body;

    // 验证必填字段
    if (!name || !company) {
      throw new ApiError(400, '姓名、公司为必填项');
    }

    // 查找并验证名片所有权
    const businessCard = await BusinessCard.findOne({
      where: { 
        id: cardId,
        user_id: req.userId
      }
    });
    
    if (!businessCard) {
      throw new ApiError(404, '名片不存在或您无权修改');
    }

    // 根据公司名获取或创建企业编码
    const companyCode = await Company.generateCompanyCode(company);

    // 更新企业自定义名称和产品中心自定义名称
    const enterpriseName = `${company}AI名片`;
    const productsName = `${company}产品中心`;
    
    await Company.update(
      {
        enterprise_name: enterpriseName.slice(0, 30),
        products_name: productsName.slice(0, 30)
      },
      { where: { company_code: companyCode } }
    );

    // 更新名片
    await businessCard.update({
      avatar, name, company, position, industry,
      mobile, wechat, email, phone, website, address, introduction,
      company_code: companyCode
    });

    // 如果是默认名片，同步更新用户表中的company_code
    if (businessCard.is_default) {
      await User.update(
        { company_code: companyCode },
        { where: { id: req.userId } }
      );
    }

    return res.json(generateResponse(0, 'success', businessCard));
  } catch (error) {
    next(error);
  }
};

/**
 * 设置默认名片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.setDefaultCard = async (req, res, next) => {
  try {
    const cardId = req.params.id;
    
    if (!cardId) {
      throw new ApiError(400, '缺少必要参数：id');
    }

    // 查找并验证名片所有权
    const businessCard = await BusinessCard.findOne({
      where: { 
        id: cardId,
        user_id: req.userId
      }
    });
    
    if (!businessCard) {
      throw new ApiError(404, '名片不存在或您无权修改');
    }

    // 开启事务
    const transaction = await db.sequelize.transaction();

    try {
      // 将所有名片设为非默认
      await BusinessCard.update(
        { is_default: false },
        { 
          where: { user_id: req.userId },
          transaction
        }
      );

      // 将选中的名片设为默认
      await businessCard.update(
        { is_default: true },
        { transaction }
      );

      // 更新用户表中的company_code
      await User.update(
        { company_code: businessCard.company_code },
        { 
          where: { id: req.userId },
          transaction
        }
      );

      // 提交事务
      await transaction.commit();

      // 重新获取更新后的名片
      const updatedCard = await BusinessCard.findByPk(cardId);

      return res.json(generateResponse(0, 'success', updatedCard));
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
};

/**
 * 删除名片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.deleteCard = async (req, res, next) => {
  try {
    const cardId = req.params.id;
    
    if (!cardId) {
      throw new ApiError(400, '缺少必要参数：id');
    }

    // 查找并验证名片所有权
    const businessCard = await BusinessCard.findOne({
      where: { 
        id: cardId,
        user_id: req.userId
      }
    });
    
    if (!businessCard) {
      throw new ApiError(404, '名片不存在或您无权删除');
    }

    // 检查用户是否至少有两张名片
    const cardCount = await BusinessCard.count({
      where: { user_id: req.userId }
    });

    // 获取force参数，如果force=1则允许删除最后一张名片
    const force = req.query.force === '1';

    // 如果不是强制删除，并且只有一张名片，则不允许删除
    if (cardCount <= 1 && !force) {
      throw new ApiError(400, '无法删除唯一的名片，请至少保留一张名片');
    }

    // 开启事务
    const transaction = await db.sequelize.transaction();

    try {
      // 如果要删除的是默认名片，并且不是最后一张名片，需要设置另一张名片为默认
      if (businessCard.is_default && cardCount > 1) {
        // 找到另一张名片
        const anotherCard = await BusinessCard.findOne({
          where: { 
            user_id: req.userId,
            id: { [Sequelize.Op.ne]: cardId }
          },
          transaction
        });

        if (anotherCard) {
          // 设置为默认名片
          await anotherCard.update(
            { is_default: true },
            { transaction }
          );

          // 更新用户表中的company_code
          await User.update(
            { company_code: anotherCard.company_code },
            { 
              where: { id: req.userId },
              transaction
            }
          );
        }
      } else if (cardCount <= 1 && force) {
        // 如果是强制删除最后一张名片，清除用户的company_code
        await User.update(
          { company_code: null },
          { 
            where: { id: req.userId },
            transaction
          }
        );
      }

      // 删除名片
      await businessCard.destroy({ transaction });

      // 删除相关的收藏记录
      await CardCollection.destroy({
        where: { card_id: cardId },
        transaction
      });

      // 提交事务
      await transaction.commit();

      return res.json(generateResponse(0, 'success', null));
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
};

/**
 * 获取指定ID的名片信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.getCardById = async (req, res, next) => {
  try {
    const cardId = req.params.id;
    
    if (!cardId) {
      throw new ApiError(400, '缺少必要参数：id');
    }

    const businessCard = await BusinessCard.findByPk(cardId);

    if (!businessCard) {
      throw new ApiError(404, '名片不存在');
    }

    const cardData = businessCard.toJSON();

    // 获取名片所属用户的AI分身URL
    const cardOwner = await User.findByPk(businessCard.user_id);
    cardData.aiAvatarUrl = cardOwner ? cardOwner.ai_avatar_url : null;

    // 检查是否有登录用户 (req.userId存在)
    if (req.userId) {
      // 检查当前用户是否已收藏该名片
      const isCollected = await CardCollection.findOne({
        where: {
          user_id: req.userId,
          card_id: cardId
        }
      });
      cardData.isCollected = !!isCollected;
    } else {
      // 未登录用户默认未收藏
      cardData.isCollected = false;
    }

    return res.json(generateResponse(0, 'success', cardData));
  } catch (error) {
    next(error);
  }
};

/**
 * 检查用户是否拥有名片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.checkUserHasCard = async (req, res, next) => {
  try {
    // 用户必须已登录
    const userId = req.userId;
    
    if (!userId) {
      return res.json(generateResponse(0, 'success', {
        isLoggedIn: false,
        hasCardInfo: false
      }));
    }
    
    // 获取用户信息
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.json(generateResponse(0, 'success', {
        isLoggedIn: false,
        hasCardInfo: false
      }));
    }
    
    // 查询用户是否有默认名片
    const businessCard = await BusinessCard.findOne({
      where: { 
        user_id: userId,
        is_default: true
      }
    });
    
    // 如果没有默认名片，查询是否有任何名片
    let cardInfo = businessCard;
    if (!cardInfo) {
      cardInfo = await BusinessCard.findOne({
        where: { user_id: userId }
      });
      
      // 如果找到名片，将其设为默认
      if (cardInfo) {
        await cardInfo.update({ is_default: true });
      }
    }
    
    // 构建响应数据
    const responseData = {
      isLoggedIn: true,
      hasCardInfo: !!cardInfo
    };
    
    // 如果存在名片，添加名片信息到响应中
    if (cardInfo) {
      responseData.cardInfo = cardInfo;
    }
    
    return res.json(generateResponse(0, 'success', responseData));
  } catch (error) {
    next(error);
  }
};

/**
 * 通过POST请求设置默认名片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.setDefaultCardPost = async (req, res, next) => {
  try {
    const { cardId } = req.body;
    
    if (!cardId) {
      throw new ApiError(400, '缺少必要参数：cardId');
    }

    // 查找并验证名片所有权
    const businessCard = await BusinessCard.findOne({
      where: { 
        id: cardId,
        user_id: req.userId
      }
    });
    
    if (!businessCard) {
      throw new ApiError(404, '名片不存在或您无权修改');
    }

    // 开启事务
    const transaction = await db.sequelize.transaction();

    try {
      // 将所有名片设为非默认
      await BusinessCard.update(
        { is_default: false },
        { 
          where: { user_id: req.userId },
          transaction
        }
      );

      // 将选中的名片设为默认
      await businessCard.update(
        { is_default: true },
        { transaction }
      );

      // 更新用户表中的company_code
      await User.update(
        { company_code: businessCard.company_code },
        { 
          where: { id: req.userId },
          transaction
        }
      );

      // 提交事务
      await transaction.commit();

      // 重新获取更新后的名片
      const updatedCard = await BusinessCard.findByPk(cardId);

      return res.json(generateResponse(0, 'success', updatedCard));
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
}; 