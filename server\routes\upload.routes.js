/**
 * 文件上传路由
 */
const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/upload.controller');
const { verifyToken } = require('../middlewares/auth.middleware');

// 配置multer中间件
const upload = uploadController.configureMulter();

// 上传头像
router.post('/avatar', verifyToken, upload.single('file'), uploadController.uploadAvatar);

// 获取七牛云上传凭证（直传方式）
router.get('/token', verifyToken, uploadController.getQiniuUploadToken);

module.exports = router; 