.container {
  padding: 0 30rpx;
  background-color: #F8FAFF;
  min-height: 100vh;
  position: relative;
}

/* 标题容器 */
.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0 40rpx;
  padding: 0 20rpx;
}

/* 标题 */
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  max-width: calc(100% - 240rpx); /* 为分享按钮预留空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 20rpx;
}

/* 页面分享按钮 */
.page-share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(62, 127, 255, 0.1);
  border: none;
  font-size: 22rpx;
  color: #3E7FFF;
  width: 220rpx !important; /* 固定宽度，确保不被挤压 */
  flex-shrink: 0; /* 防止被压缩 */
}

.page-share-button::after {
  border: none;
}

.share-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3E7FFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 产品列表 */
.products-list {
  padding-bottom: 40rpx;
}

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  bottom: 80rpx;
  right: 0;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  z-index: 99999;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 12rpx 0 0 12rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 1px solid rgba(230, 230, 230, 0.8);
}

/* 导航栏收起状态 */
.nav-bar.collapsed {
  width: auto;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.nav-bar.collapsed .nav-buttons {
  display: none;
}

.nav-bar.collapsed .toggle-btn {
  border: 1px solid rgba(230, 230, 230, 0.8);
  border-right: none;
}

/* 导航按钮区域 */
.nav-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6rpx 18rpx;
}

/* 单个导航按钮 */
.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 16rpx;
}

/* 按钮图标 */
.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 3rpx;
}

/* 按钮文字 */
.btn-text {
  font-size: 20rpx;
  color: #3E7FFF;
  text-align: center;
}

/* 切换按钮 */
.toggle-btn {
  padding: 9rpx 7rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx 0 0 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: -4rpx 0 10rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(230, 230, 230, 0.8);
  border-right: none;
  z-index: 10000;
  min-width: 38rpx;
  position: relative;
}

/* 切换按钮文字 */
.toggle-text {
  font-size: 20rpx;
  color: #3E7FFF;
  text-align: center;
}

/* 垂直布局的toggle-text */
.toggle-text.vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 30rpx;
  padding: 5rpx 0;
  min-height: 90rpx;
}

/* 按钮中的字 */
.toggle-char {
  font-size: 18rpx;
  color: #3E7FFF;
  text-align: center;
  line-height: 1.1;
  padding: 1rpx 0;
  display: block;
}

.product-item {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 产品封面图 */
.product-cover {
  width: 250rpx; /* 5:4比例 */
  height: 200rpx;
  flex-shrink: 0;
  background-color: #f0f0f0;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 确保图片填满区域 */
}

/* 产品信息 */
.product-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  position: relative;
}

.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-description {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
  margin-bottom: 60rpx; /* 为底部按钮留出空间 */
}

/* 操作按钮 */
.product-actions {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  display: flex;
  justify-content: flex-end;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
  background-color: transparent;
  border: none;
  font-size: 24rpx;
}

.action-button::after {
  border: none;
}

.share-button {
  color: #3E7FFF;
}

.view-button {
  color: #3E7FFF;
  background-color: rgba(62, 127, 255, 0.1);
}

.action-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

/* 加载状态 */
.loading-container {
  width: 100%;
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.loading-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3E7FFF;
  animation: bounce 1.4s infinite ease-in-out both;
  opacity: 0.6;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* WebView样式 - 全屏显示 */
.full-webview {
  width: 100%;
  height: 100%;
  flex: 1;
}

/* 默认页面样式 */
.default-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  height: 80vh;
  width: 100%;
  box-sizing: border-box;
}

.default-image {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 40rpx;
}

.default-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  text-align: center;
}

.default-subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 二维码样式 */
.contact-qrcode {
  width: 300rpx;
  height: 300rpx;
  margin: 30rpx 0;
}

.qrcode-tip {
  font-size: 26rpx;
  color: #999;
  text-align: center;
}

/* 返回名片按钮样式 */
.back-to-card {
  display: flex;
  justify-content: center;
  margin: 10rpx 0;
}

.back-button {
  background-color: #3E7FFF;
  color: white;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 40rpx;
  line-height: 1.5;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 导航按钮区域中的分享按钮 */
.nav-btn-wrapper.share-btn {
  padding: 0;
  margin: 0 16rpx;
  background: none;
  border: none;
  outline: none;
  box-sizing: border-box;
  line-height: normal;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: auto;
  font-size: inherit;
  color: inherit;
  width: auto;
  font-weight: normal;
}

.nav-btn-wrapper.share-btn::after {
  border: none;
  outline: none;
  border-radius: 0;
}

.nav-btn-wrapper .nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}