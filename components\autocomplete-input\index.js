/**
 * 自动完成输入框组件
 */
const request = require('../../utils/request');

Component({
  properties: {
    // 输入框的值
    value: {
      type: String,
      value: ''
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: '请输入'
    },
    // 是否必填
    required: {
      type: Boolean,
      value: false
    },
    // 标签文本
    label: {
      type: String,
      value: ''
    },
    // 搜索API的URL
    searchUrl: {
      type: String,
      value: '/company/search'
    },
    // 搜索参数名
    searchParam: {
      type: String,
      value: 'keyword'
    },
    // 结果显示的字段名
    displayField: {
      type: String,
      value: 'name'
    },
    // 值字段名
    valueField: {
      type: String,
      value: 'name'
    },
    // 最小搜索字符数
    minSearchLength: {
      type: Number,
      value: 1
    },
    // 搜索延迟（毫秒）
    searchDelay: {
      type: Number,
      value: 300
    }
  },

  data: {
    // 搜索结果列表
    suggestions: [],
    // 是否显示下拉列表
    showDropdown: false,
    // 是否正在搜索
    isSearching: false,
    // 输入框是否获得焦点
    isFocused: false
  },

  lifetimes: {
    attached() {
      // 初始化搜索防抖定时器
      this.searchTimer = null;
    },
    
    detached() {
      // 清理定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
    }
  },

  methods: {
    /**
     * 输入框输入事件
     */
    onInput(e) {
      const value = e.detail.value;
      
      // 触发父组件的输入事件
      this.triggerEvent('input', {
        value: value
      });

      // 执行搜索
      this.performSearch(value);
    },

    /**
     * 输入框获得焦点
     */
    onFocus() {
      this.setData({
        isFocused: true
      });
      
      // 如果有值且有搜索结果，显示下拉列表
      if (this.properties.value && this.data.suggestions.length > 0) {
        this.setData({
          showDropdown: true
        });
      }
    },

    /**
     * 输入框失去焦点
     */
    onBlur() {
      this.setData({
        isFocused: false
      });
      // 不立即隐藏下拉列表，让用户可以滑动选择
    },

    /**
     * 执行搜索
     */
    performSearch(keyword) {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 如果关键词长度不足，清空结果
      if (!keyword || keyword.length < this.properties.minSearchLength) {
        this.setData({
          suggestions: [],
          showDropdown: false,
          isSearching: false
        });
        return;
      }

      // 设置搜索状态
      this.setData({
        isSearching: true
      });

      // 防抖搜索
      this.searchTimer = setTimeout(() => {
        this.doSearch(keyword);
      }, this.properties.searchDelay);
    },

    /**
     * 执行实际搜索
     */
    async doSearch(keyword) {
      try {
        const params = {};
        params[this.properties.searchParam] = keyword;

        const response = await request.get(this.properties.searchUrl, params);

        console.log('搜索响应:', response);

        // 判断response是否是数组（直接返回的data）还是包含code的对象
        if (Array.isArray(response)) {
          // 直接返回的是数组数据
          console.log('搜索成功，数据:', response);
          this.setData({
            suggestions: response || [],
            showDropdown: response && response.length > 0,
            isSearching: false
          });
        } else if (response && response.code === 0) {
          // 返回的是包含code的对象
          console.log('搜索成功，数据:', response.data);
          this.setData({
            suggestions: response.data || [],
            showDropdown: response.data && response.data.length > 0,
            isSearching: false
          });
        } else {
          console.error('搜索失败:', response ? response.message : '未知错误');
          this.setData({
            suggestions: [],
            showDropdown: false,
            isSearching: false
          });
        }
      } catch (error) {
        console.error('搜索请求失败:', error);
        this.setData({
          suggestions: [],
          showDropdown: false,
          isSearching: false
        });
      }
    },

    /**
     * 选择搜索结果项
     */
    onSelectItem(e) {
      const index = e.currentTarget.dataset.index;
      const item = this.data.suggestions[index];
      
      if (item) {
        const value = item[this.properties.valueField];
        
        // 触发选择事件
        this.triggerEvent('select', {
          value: value,
          item: item
        });

        // 隐藏下拉列表
        this.setData({
          showDropdown: false,
          suggestions: []
        });
      }
    },

    /**
     * 清空输入
     */
    onClear() {
      this.triggerEvent('input', {
        value: ''
      });

      this.setData({
        suggestions: [],
        showDropdown: false
      });
    },

    /**
     * 点击组件外部区域，隐藏下拉列表
     */
    onClickOutside() {
      this.setData({
        showDropdown: false
      });
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止事件冒泡，防止触发外部点击事件
    }
  }
});
