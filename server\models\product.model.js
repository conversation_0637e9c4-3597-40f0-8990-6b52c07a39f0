/**
 * 产品模型
 */
module.exports = (sequelize, DataTypes) => {
  const Product = sequelize.define('Product', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    company_code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '关联的企业编码'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '产品名称'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '产品简介'
    },
    cover_image: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '产品封面图URL'
    },
    product_url: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '产品详情页链接'
    },
    share_image: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '产品分享图URL'
    },
    display_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '产品展示顺序'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'products',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return Product;
}; 