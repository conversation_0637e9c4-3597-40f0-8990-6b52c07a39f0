// 调试Token过期问题的辅助脚本
// 在微信开发者工具的控制台中运行以下代码来测试修复效果

// 1. 检查当前登录状态
function checkLoginStatus() {
  const app = getApp();
  console.log('=== 当前登录状态检查 ===');
  console.log('globalData.token:', app.globalData.token ? '存在' : '不存在');
  console.log('globalData.userInfo:', app.globalData.userInfo);
  console.log('localStorage token:', wx.getStorageSync('token') ? '存在' : '不存在');
  console.log('localStorage userInfo:', wx.getStorageSync('userInfo'));
  console.log('TabBar控制状态:', app.globalData.tabBarControl);
}

// 2. 模拟token过期情况
function simulateTokenExpiry() {
  console.log('=== 模拟Token过期 ===');
  const app = getApp();
  
  // 保存原始token用于恢复
  const originalToken = app.globalData.token;
  const originalUserInfo = app.globalData.userInfo;
  
  // 设置一个无效的token
  app.globalData.token = 'invalid_token_for_testing';
  wx.setStorageSync('token', 'invalid_token_for_testing');
  
  console.log('已设置无效token，现在尝试发起需要认证的请求...');
  
  // 尝试获取用户信息，这应该会触发401错误处理
  const auth = require('./utils/auth.js');
  auth.getUserInfo().then(res => {
    console.log('意外：获取用户信息成功', res);
  }).catch(err => {
    console.log('预期：获取用户信息失败', err);
    console.log('检查是否正确清理了登录状态...');
    setTimeout(() => {
      checkLoginStatus();
    }, 1000);
  });
  
  // 提供恢复方法
  window.restoreToken = function() {
    app.globalData.token = originalToken;
    app.globalData.userInfo = originalUserInfo;
    if (originalToken) {
      wx.setStorageSync('token', originalToken);
    }
    if (originalUserInfo) {
      wx.setStorageSync('userInfo', originalUserInfo);
    }
    console.log('已恢复原始token');
  };
}

// 3. 清理所有登录状态（用于测试清理功能）
function clearAllLoginState() {
  console.log('=== 清理所有登录状态 ===');
  const app = getApp();
  
  // 调用app的清理方法
  if (typeof app.clearLoginState === 'function') {
    app.clearLoginState();
    console.log('已调用app.clearLoginState()');
  } else {
    // 手动清理
    app.globalData.token = '';
    app.globalData.userInfo = null;
    app.globalData.cardInfo = null;
    app.globalData.companyInfo = null;
    app.globalData.cardUpdated = false;
    
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('cardInfo');
    wx.removeStorageSync('companyInfo');
    
    console.log('已手动清理登录状态');
  }
  
  setTimeout(() => {
    checkLoginStatus();
  }, 500);
}

// 4. 测试请求错误处理
function testRequestErrorHandling() {
  console.log('=== 测试请求错误处理 ===');
  const request = require('./utils/request.js');
  
  // 发起一个会返回401的请求
  request.get('/auth/user', {}, true).then(res => {
    console.log('意外：请求成功', res);
  }).catch(err => {
    console.log('预期：请求失败', err);
    if (err.code === 401) {
      console.log('✓ 正确处理了401错误');
    } else {
      console.log('✗ 401错误处理可能有问题');
    }
  });
}

// 5. 检查TabBar状态
function checkTabBarStatus() {
  console.log('=== 检查TabBar状态 ===');
  const app = getApp();
  console.log('全局TabBar控制状态:', app.globalData.tabBarControl);
  
  // 尝试获取当前页面的TabBar
  const pages = getCurrentPages();
  if (pages && pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    if (currentPage && typeof currentPage.getTabBar === 'function') {
      const tabBar = currentPage.getTabBar();
      if (tabBar) {
        console.log('当前页面TabBar数据:', tabBar.data);
      } else {
        console.log('当前页面没有TabBar组件');
      }
    } else {
      console.log('当前页面不支持getTabBar方法');
    }
  } else {
    console.log('没有找到当前页面');
  }
}

// 6. 测试登录流程
function testLoginFlow() {
  console.log('=== 测试登录流程 ===');

  // 首先清理登录状态
  clearAllLoginState();

  setTimeout(() => {
    console.log('准备跳转到登录页面...');
    wx.navigateTo({
      url: '/pages/login/index',
      success: () => {
        console.log('✓ 成功跳转到登录页面');
        console.log('请手动完成登录流程，然后观察页面状态变化');
      },
      fail: (err) => {
        console.log('✗ 跳转到登录页面失败', err);
      }
    });
  }, 1000);
}

// 7. 强制同步页面状态
function forceSyncPageStatus() {
  console.log('=== 强制同步页面状态 ===');
  const pages = getCurrentPages();
  if (pages && pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    if (currentPage && typeof currentPage.syncLoginStatus === 'function') {
      console.log('调用当前页面的syncLoginStatus方法');
      currentPage.syncLoginStatus();
    } else {
      console.log('当前页面没有syncLoginStatus方法');
    }
  } else {
    console.log('没有找到当前页面');
  }
}

// 8. 检查页面状态与全局状态的一致性
function checkStateConsistency() {
  console.log('=== 检查状态一致性 ===');
  const app = getApp();
  const pages = getCurrentPages();

  console.log('全局状态:');
  console.log('  token:', app.globalData.token ? '存在' : '不存在');
  console.log('  userInfo:', app.globalData.userInfo ? '存在' : '不存在');
  console.log('  cardInfo:', app.globalData.cardInfo ? '存在' : '不存在');

  console.log('本地存储:');
  console.log('  token:', wx.getStorageSync('token') ? '存在' : '不存在');
  console.log('  userInfo:', wx.getStorageSync('userInfo') ? '存在' : '不存在');

  if (pages && pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    if (currentPage && currentPage.data) {
      console.log('当前页面状态:');
      console.log('  hasUserInfo:', currentPage.data.hasUserInfo);
      console.log('  hasCardInfo:', currentPage.data.hasCardInfo);
      console.log('  isLoggingIn:', currentPage.data.isLoggingIn);
      console.log('  isShowingTokenExpiredModal:', currentPage.data.isShowingTokenExpiredModal);

      // 检查一致性
      const globalHasUser = !!app.globalData.userInfo;
      const pageHasUser = !!currentPage.data.hasUserInfo;

      if (globalHasUser !== pageHasUser) {
        console.log('⚠️ 状态不一致：全局有用户信息但页面状态不匹配');
        console.log('建议运行 forceSyncPageStatus() 来修复');
      } else {
        console.log('✓ 状态一致');
      }

      // 检查模态框状态
      if (currentPage.data.isShowingTokenExpiredModal) {
        console.log('⚠️ 检测到token过期模态框正在显示');
        if (globalHasUser) {
          console.log('⚠️ 用户已登录但模态框仍在显示，建议运行 forceSyncPageStatus() 修复');
        }
      }
    }
  }
}

// 9. 检查和修复模态框状态
function checkModalStatus() {
  console.log('=== 检查模态框状态 ===');
  const pages = getCurrentPages();

  if (pages && pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    if (currentPage && currentPage.data) {
      const isShowingModal = currentPage.data.isShowingTokenExpiredModal;
      console.log('模态框显示状态:', isShowingModal);

      if (isShowingModal) {
        const app = getApp();
        const hasValidLogin = (app.globalData.token || wx.getStorageSync('token')) &&
                             (app.globalData.userInfo || wx.getStorageSync('userInfo'));

        if (hasValidLogin) {
          console.log('⚠️ 用户已登录但模态框仍在显示，自动修复...');
          currentPage.setData({
            isShowingTokenExpiredModal: false
          });

          // 清理可能的定时器
          if (currentPage.tokenExpiredCheckTimer) {
            clearTimeout(currentPage.tokenExpiredCheckTimer);
            currentPage.tokenExpiredCheckTimer = null;
            console.log('已清理定时器');
          }

          // 强制同步状态
          if (typeof currentPage.syncLoginStatus === 'function') {
            currentPage.syncLoginStatus();
          }

          console.log('✓ 模态框状态已修复');
        } else {
          console.log('✓ 用户未登录，模态框显示正常');
        }
      } else {
        console.log('✓ 模态框未显示');
      }
    }
  }
}

// 导出测试方法到全局，方便在控制台调用
window.debugTokenExpiry = {
  checkLoginStatus,
  simulateTokenExpiry,
  clearAllLoginState,
  testRequestErrorHandling,
  checkTabBarStatus,
  testLoginFlow,
  forceSyncPageStatus,
  checkStateConsistency,
  checkModalStatus
};

console.log('Token过期调试工具已加载');
console.log('使用方法：');
console.log('debugTokenExpiry.checkLoginStatus() - 检查当前登录状态');
console.log('debugTokenExpiry.simulateTokenExpiry() - 模拟token过期');
console.log('debugTokenExpiry.clearAllLoginState() - 清理所有登录状态');
console.log('debugTokenExpiry.testRequestErrorHandling() - 测试请求错误处理');
console.log('debugTokenExpiry.checkTabBarStatus() - 检查TabBar状态');
console.log('debugTokenExpiry.testLoginFlow() - 测试登录流程');
console.log('debugTokenExpiry.forceSyncPageStatus() - 强制同步页面状态');
console.log('debugTokenExpiry.checkStateConsistency() - 检查状态一致性');
console.log('debugTokenExpiry.checkModalStatus() - 检查和修复模态框状态');
