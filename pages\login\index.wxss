/* 登录页专用容器样式 */
.login-page-container {
  min-height: 100vh;
  background-color: #f8faff;
  box-sizing: border-box;
}

/* 登录页样式 */
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 60rpx 50rpx 120rpx 50rpx;
  box-sizing: border-box;
  position: relative;
  background: linear-gradient(135deg, #f6f9ff 0%, #ffffff 100%);
}

.login-container::before {
  content: '';
  position: absolute;
  top: -200rpx;
  right: -200rpx;
  width: 600rpx;
  height: 600rpx;
  border-radius: 300rpx;
  background: linear-gradient(135deg, rgba(62, 127, 255, 0.08) 0%, rgba(62, 127, 255, 0.03) 100%);
  z-index: 0;
}

.login-container::after {
  content: '';
  position: absolute;
  bottom: -300rpx;
  left: -200rpx;
  width: 700rpx;
  height: 700rpx;
  border-radius: 350rpx;
  background: linear-gradient(135deg, rgba(62, 127, 255, 0.06) 0%, rgba(62, 127, 255, 0.02) 100%);
  z-index: 0;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  margin-top: 80rpx;
  margin-bottom: 40rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  filter: drop-shadow(0 8rpx 16rpx rgba(62, 127, 255, 0.2));
}

.title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  background: linear-gradient(90deg, #3E7FFF, #6B9FFF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.subtitle {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 0;
}

/* 功能特性区域 */
.login-features {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 40rpx 0 60rpx 0;
  z-index: 1;
  flex: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.8);
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(62, 127, 255, 0.08);
}

.feature-icon {
  width: 56rpx;
  height: 56rpx;
  margin-right: 20rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 底部登录区域 */
.login-footer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  color: #fff;
  font-size: 32rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 32rpx;
  margin: 0 0 20rpx 0;
  box-sizing: border-box;
  min-width: 400rpx;
}

.login-btn-active {
  background: linear-gradient(90deg, #07C160, #10D86B);
  box-shadow: 0 8rpx 20rpx rgba(7, 193, 96, 0.2);
}

.login-btn-disabled {
  background: linear-gradient(90deg, #B7B7B7, #CCCCCC);
  box-shadow: 0 8rpx 20rpx rgba(150, 150, 150, 0.2);
}

.login-btn::after {
  border: none;
}

.wechat-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  filter: brightness(0) invert(1);
}

.login-text {
  font-size: 32rpx;
  color: #fff;
  white-space: nowrap;
  line-height: 1;
}

.privacy-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
}

.privacy-link {
  color: #3E7FFF;
}

/* 协议勾选区样式 */
.agreement-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
}

.agreement-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.agreement-checkbox {
  transform: scale(0.7);
  margin-right: 10rpx;
}

/* iPad和大屏设备适配 */
@media screen and (min-width: 768px) {
  .login-container {
    max-width: 600rpx;
    margin: 0 auto;
    padding: 80rpx 60rpx 160rpx 60rpx;
  }

  .login-btn {
    min-width: 500rpx;
    height: 108rpx;
    font-size: 36rpx;
    padding: 0 48rpx;
  }

  .login-text {
    font-size: 36rpx;
    white-space: nowrap;
  }

  .wechat-icon {
    width: 56rpx;
    height: 56rpx;
    margin-right: 20rpx;
  }

  .privacy-text {
    font-size: 26rpx;
  }
}