/**
 * 统一分享模块
 * 用于处理所有页面的分享功能，确保携带正确的参数和一致的体验
 * 
 * 使用方法:
 * 1. 在页面中引入模块: const share = require('../../utils/share');
 * 2. 在onShareAppMessage方法中使用: return share.handleShare({ type, data });
 * 3. 在分享按钮点击事件中使用: share.handleShareButtonClick();
 */

const app = getApp();

/**
 * 处理页面分享
 * @param {Object} options - 分享选项
 * @param {string} options.type - 分享类型: 'card'（名片）, 'enterprise'（企业）, 'products'（产品中心）, 'product'（单个产品）
 * @param {Object} options.data - 分享数据
 * @param {Object} options.page - 当前页面实例，用于获取页面数据
 * @returns {Object} 包含title, path和imageUrl的分享配置对象
 */
function handleShare(options) {
  const { type, data, page } = options;
  
  // 记录分享信息用于调试
  console.log('统一分享模块 - 分享类型:', type);
  console.log('统一分享模块 - 分享数据:', data);
  
  // 根据不同分享类型构造不同的分享路径和参数
  switch (type) {
    case 'card':
      return shareCard(data);
    case 'enterprise':
      return shareEnterprise(data);
    case 'products':
      return shareProducts(data);
    case 'product':
      return shareProduct(data);
    default:
      console.error('未知的分享类型:', type);
      // 默认分享当前页面
      return getDefaultShare();
  }
}

/**
 * 分享名片
 * @param {Object} data - 名片数据
 */
function shareCard(data) {
  const { cardInfo, shareImagePath } = data;

  if (!cardInfo || !cardInfo.id) {
    console.error('分享名片失败：缺少有效的名片信息');
    return getDefaultShare();
  }

  return {
    title: `${cardInfo.name || ''}的名片`,
    path: `/pages/card-detail/index?id=${cardInfo.id}&fromShare=true`,
    imageUrl: shareImagePath || cardInfo.avatar || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-share.png'
  };
}

/**
 * 分享企业主页
 * @param {Object} data - 企业数据
 */
function shareEnterprise(data) {
  const { cardInfo, companyInfo, enterpriseName } = data;

  console.log('分享企业主页，数据:', data);

  if (!companyInfo || !companyInfo.companyCode) {
    console.error('分享企业主页失败：缺少有效的企业信息或企业代码');
    return getDefaultShare();
  }

  const title = enterpriseName || companyInfo.enterpriseName || '企业主页';
  const imageUrl = companyInfo.enterpriseShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-enterprise.jpg';

  // 直接分享到企业页面，类似产品中心的分享方式
  let path = `/pages/enterprise/index?companyCode=${companyInfo.companyCode}&fromShare=true`;

  // 如果有名片ID，添加到路径
  if (cardInfo && cardInfo.id) {
    path += `&cardId=${cardInfo.id}`;
    console.log('添加卡片ID到企业分享路径:', cardInfo.id);
  }

  console.log('最终企业分享路径:', path);

  return {
    title: title,
    path: path,
    imageUrl: imageUrl
  };
}

/**
 * 分享产品中心
 * @param {Object} data - 产品中心数据
 */
function shareProducts(data) {
  const { cardInfo, companyCode, productsName, productsShareImage, fromPersonal } = data;
  
  console.log('分享产品中心，数据:', data);
  
  if (!companyCode) {
    console.error('分享产品中心失败：缺少企业代码');
    return getDefaultShare();
  }
  
  const title = productsName || '产品中心';
  const imageUrl = productsShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-products.jpg';
  
  let path = `/pages/products/index?companyCode=${companyCode}&fromShare=true`;
  
  // 如果有名片ID，添加到路径
  if (cardInfo && cardInfo.id) {
    path += `&cardId=${cardInfo.id}`;
    console.log('添加卡片ID到产品中心分享路径:', cardInfo.id);
  }
  
  // 如果来自个人页面，添加fromPersonal参数
  if (fromPersonal) {
    path += `&fromPersonal=true`;
    console.log('添加fromPersonal标识到产品中心分享路径');
  }
  
  console.log('产品中心最终分享路径:', path);
  
  return {
    title: title,
    path: path,
    imageUrl: imageUrl
  };
}

/**
 * 分享单个产品
 * @param {Object} data - 产品数据
 */
function shareProduct(data) {
  const { productUrl, productName, productImage, cardId, companyCode, fromPersonal } = data;
  
  console.log('分享产品，数据:', data);
  
  if (!productUrl) {
    console.error('分享产品失败：缺少产品URL');
    return getDefaultShare();
  }
  
  let path = `/pages/products/detail?url=${encodeURIComponent(productUrl)}&fromShare=true`;
  
  // 添加卡片ID参数（如果有）
  if (cardId) {
    path += `&cardId=${cardId}`;
    console.log('添加卡片ID到分享路径:', cardId);
  } else {
    console.warn('分享产品没有提供cardId，可能导致无法返回名片');
  }
  
  // 添加公司代码参数（如果有）
  if (companyCode) {
    path += `&companyCode=${companyCode}`;
    console.log('添加公司代码到分享路径:', companyCode);
  } else {
    console.warn('分享产品没有提供companyCode，可能导致无法查看产品中心');
  }
  
  // 如果来自个人页面，添加fromPersonal参数
  if (fromPersonal) {
    path += `&fromPersonal=true`;
    console.log('添加fromPersonal标识到产品分享路径');
  }
  
  // 添加产品名称和图片到路径参数中，确保在多级分享中不会丢失
  if (productName) {
    path += `&productName=${encodeURIComponent(productName)}`;
  }
  
  if (productImage) {
    path += `&productImage=${encodeURIComponent(productImage)}`;
  }
  
  console.log('最终分享路径:', path);
  
  return {
    title: productName || '产品详情',
    path: path,
    imageUrl: productImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-share.png'
  };
}

/**
 * 获取默认的分享配置
 * @returns {Object} 默认分享配置
 */
function getDefaultShare() {
  return {
    title: '智链名片',
    path: '/pages/index/index',
    imageUrl: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-share.png'
  };
}

/**
 * 处理分享按钮点击
 * 调起系统分享
 * @deprecated 建议直接使用微信小程序的button组件，设置open-type="share"属性
 */
function handleShareButtonClick() {
  // 仅调起系统分享菜单，不显示任何提示
  wx.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage']
  });
}

module.exports = {
  handleShare,
  handleShareButtonClick,
  getDefaultShare
}; 