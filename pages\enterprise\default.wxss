.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  background-color: #3E7FFF;
  padding: 40rpx 30rpx;
  color: #fff;
}

.company-profile {
  display: flex;
  align-items: center;
}

.company-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  background-color: #fff;
  padding: 10rpx;
  margin-right: 30rpx;
}

.company-info {
  flex: 1;
}

.company-name {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.company-slogan {
  font-size: 28rpx;
  opacity: 0.9;
}

.section {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #3E7FFF;
}

.intro-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  text-align: justify;
}

.advantage-list {
  display: flex;
  flex-direction: column;
}

.advantage-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.advantage-item:last-child {
  border-bottom: none;
}

.advantage-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.advantage-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.contact-list {
  display: flex;
  flex-direction: column;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.contact-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
} 