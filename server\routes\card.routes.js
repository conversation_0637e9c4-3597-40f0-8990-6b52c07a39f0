/**
 * 名片路由
 */
const express = require('express');
const router = express.Router();
const cardController = require('../controllers/card.controller');
const { verifyToken, optionalVerifyToken } = require('../middlewares/auth.middleware');

// 获取当前用户名片
router.get('/', verifyToken, cardController.getUserCard);

// 获取当前用户所有名片列表
router.get('/list', verifyToken, cardController.getAllUserCards);

// 检查用户是否拥有名片
router.get('/check-status', verifyToken, cardController.checkUserHasCard);

// 创建新名片
router.post('/create', verifyToken, cardController.createNewCard);

// 创建/更新默认名片
router.post('/', verifyToken, cardController.createOrUpdateCard);

// 更新指定ID的名片
router.put('/:id', verifyToken, cardController.updateCardById);

// 设置默认名片
router.put('/:id/default', verifyToken, cardController.setDefaultCard);

// 设置默认名片（POST方法）
router.post('/setDefault', verifyToken, cardController.setDefaultCardPost);

// 删除名片
router.delete('/:id', verifyToken, cardController.deleteCard);

// 获取指定ID的名片（使用可选验证，既支持未登录用户访问，也能识别已登录用户）
router.get('/:id', optionalVerifyToken, cardController.getCardById);

module.exports = router; 