-- 项目中心清理SQL脚本

-- 备份数据库（可选，建议在执行前进行备份）
-- 例如：mysqldump -u username -p database_name > backup_before_cleanup.sql

-- 1. 删除companies表中的projects_share_image字段和projects_name字段
-- 注意：MySQL的ALTER TABLE DROP COLUMN不支持IF EXISTS语法
-- 可以先检查字段是否存在，再删除
-- 方法1：使用存储过程安全删除字段
DELIMITER //
DROP PROCEDURE IF EXISTS drop_column_if_exists //
CREATE PROCEDURE drop_column_if_exists()
BEGIN
    DECLARE CONTINUE HANDLER FOR 1091 BEGIN END;  -- 忽略"列不存在"错误
    
    -- 尝试删除projects_share_image字段
    ALTER TABLE companies DROP COLUMN projects_share_image;
    
    -- 尝试删除projects_name字段
    ALTER TABLE companies DROP COLUMN projects_name;
END //
DELIMITER ;

CALL drop_column_if_exists();
DROP PROCEDURE IF EXISTS drop_column_if_exists;

-- 方法2：直接执行删除（如果字段不存在会报错，但不会影响后续语句执行）
-- ALTER TABLE companies DROP COLUMN projects_share_image;
-- ALTER TABLE companies DROP COLUMN projects_name;

-- 2. 删除projects表相关的索引（如果存在）
-- 注意：删除表时会自动删除表上的索引，但如果有其他表引用了这些索引，需要先删除这些引用

-- 3. 删除projects表
DROP TABLE IF EXISTS projects;

-- 4. 检查是否有外键约束引用了projects表
-- 如果有其他表引用了projects表的外键，需要先删除这些外键约束
-- 可以通过以下查询找到引用projects表的外键：
-- SELECT TABLE_NAME, CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
-- WHERE REFERENCED_TABLE_NAME = 'projects';

-- 5. 删除可能存在的视图
DROP VIEW IF EXISTS projects_view;
DROP VIEW IF EXISTS project_details_view;

-- 6. 删除可能存在的存储过程或函数
DROP PROCEDURE IF EXISTS get_projects;
DROP PROCEDURE IF EXISTS update_project;
DROP PROCEDURE IF EXISTS delete_project;
DROP FUNCTION IF EXISTS get_project_count;

-- 7. 确认删除成功
SELECT 'Projects table and related components have been successfully removed!' AS message;

-- 8. 检查数据库中是否还有其他与项目相关的对象
-- 可以通过以下查询检查表名中包含'project'的表：
-- SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
-- WHERE TABLE_NAME LIKE '%project%' AND TABLE_SCHEMA = 'your_database_name';

-- 可以通过以下查询检查列名中包含'project'的列：
-- SELECT TABLE_NAME, COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE COLUMN_NAME LIKE '%project%' AND TABLE_SCHEMA = 'your_database_name'; 