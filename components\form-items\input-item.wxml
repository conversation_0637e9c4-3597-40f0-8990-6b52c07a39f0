<view class="input-container">
  <view class="input-label">
    <text class="required" wx:if="{{required}}">*</text>
    <text>{{label}}</text>
  </view>
  <view class="input-wrapper">
    <input 
      class="input-control" 
      type="{{type}}" 
      value="{{value}}" 
      placeholder="{{placeholder}}" 
      maxlength="{{maxlength}}" 
      disabled="{{disabled}}" 
      focus="{{focus}}" 
      bindinput="onInput" 
      bindfocus="onFocus" 
      bindblur="onBlur"
    />
    <view class="clear-button" wx:if="{{showClear}}" bindtap="onClear">
      <view class="clear-icon"></view>
    </view>
  </view>
</view> 