Component({
  /**
   * 组件的属性列表
   */
  properties: {
    label: {
      type: String,
      value: ''
    },
    placeholder: {
      type: String,
      value: '请选择'
    },
    name: {
      type: String,
      value: ''
    },
    value: {
      type: String,
      value: ''
    },
    required: {
      type: Boolean,
      value: false
    },
    disabled: {
      type: Boolean,
      value: false
    },
    options: {
      type: Array,
      value: []
    },
    // 显示值的字段名，用于从options中提取要显示的文本
    labelKey: {
      type: String,
      value: 'label'
    },
    // 实际值的字段名，用于从options中提取要传递的值
    valueKey: {
      type: String,
      value: 'value'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showPopup: false,
    selectedLabel: ''
  },

  observers: {
    // 监听value和options变化，更新显示文本
    'value, options': function(value, options) {
      this.updateSelectedLabel();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 更新选中项的显示文本
    updateSelectedLabel() {
      const { value, options, labelKey, valueKey } = this.properties;
      
      if (value && options.length > 0) {
        const selectedOption = options.find(option => option[valueKey] === value);
        if (selectedOption) {
          this.setData({
            selectedLabel: selectedOption[labelKey]
          });
          return;
        }
      }
      
      this.setData({
        selectedLabel: ''
      });
    },
    
    // 显示选择弹窗
    onShowPopup() {
      if (this.properties.disabled) {
        return;
      }
      
      this.setData({
        showPopup: true
      });
    },
    
    // 关闭选择弹窗
    onClosePopup() {
      this.setData({
        showPopup: false
      });
    },
    
    // 选择选项
    onSelectOption(e) {
      const { index } = e.currentTarget.dataset;
      const { options, labelKey, valueKey, name } = this.properties;
      const selectedOption = options[index];
      
      this.setData({
        selectedLabel: selectedOption[labelKey],
        showPopup: false
      });
      
      this.triggerEvent('select', {
        name,
        value: selectedOption[valueKey],
        selectedOption
      });
    }
  }
}) 