<view class="container">
  <view class="page-header">
    <view class="page-title">切换名片</view>
    <view class="page-subtitle">选择您想要使用的名片</view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <text class="loading-text">正在加载名片...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!isLoading && isEmpty}}">
    <image class="empty-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/empty-card.png" mode="aspectFit"></image>
    <text class="empty-text">您还没有创建名片</text>
    <button class="create-btn" bindtap="createNewCard">创建新名片</button>
  </view>

  <!-- 名片列表 -->
  <view class="card-list" wx:if="{{!isLoading && !isEmpty}}">
    <view class="card-item {{item.id === currentCardId ? 'card-selected' : ''}}" 
          wx:for="{{cards}}" 
          wx:key="id">
      
      <!-- 名片主体内容，点击切换名片 -->
      <view class="card-content" bindtap="selectCard" data-id="{{item.id}}">
        <view class="card-avatar">
          <image src="{{item.avatar || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-avatar.png'}}" mode="aspectFill"></image>
        </view>
        
        <view class="card-info">
          <view class="card-name">{{item.name}}</view>
          <view class="card-company">{{item.company}}</view>
          <view class="card-position">{{item.position}}</view>
        </view>
        
        <view class="card-status" wx:if="{{item.id === currentCardId}}">
          <image class="status-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/selected.png" mode="aspectFit"></image>
          <text class="status-text">当前使用</text>
        </view>
      </view>
      
      <!-- 操作按钮区域 -->
      <view class="card-actions">
        <view class="action-btn edit-btn" catchtap="editCard" data-card-id="{{item.id}}">
          <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/edit.png" mode="aspectFit"></image>
          <text>编辑</text>
        </view>
        <view class="action-btn delete-btn" catchtap="deleteCard" data-card-id="{{item.id}}">
          <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/delete.png" mode="aspectFit"></image>
          <text>删除</text>
        </view>
      </view>
    </view>
    
    <!-- 创建新名片按钮 -->
    <view class="create-card-btn" bindtap="createNewCard">
      <image class="plus-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/plus.png" mode="aspectFit"></image>
      <text>创建新名片</text>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-btn-wrapper">
    <button class="back-btn" bindtap="goBack">返回</button>
  </view>
</view> 