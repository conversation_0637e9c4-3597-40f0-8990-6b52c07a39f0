const app = getApp()

Page({
  data: {
    // 表单数据
    formData: {
      avatar: '', // 头像
      name: '', // 姓名
      company: '', // 公司
      position: '', // 职位
      industry: '', // 行业
      mobile: '', // 手机号
      wechat: '', // 微信号
      email: '', // 邮箱
      phone: '', // 座机
      website: '', // 网址
      address: '', // 地址
      introduction: '' // 业务简介
    },
    // 行业选项
    industries: [
      '互联网/IT/电子/通信',
      '金融/投资/保险',
      '房地产/建筑',
      '商业服务/专业服务',
      '贸易/批发/零售/快消',
      '制造业',
      '医疗/健康/制药',
      '教育/培训',
      '文化/传媒/娱乐/体育',
      '能源/矿产/环保',
      '交通/物流/运输',
      '政府/非盈利组织',
      '农/林/牧/渔',
      '其他'
    ],
    rules: {
      name: [{ required: true, message: '请输入姓名' }],
      company: [{ required: true, message: '请输入公司名称' }]
    },
    isSubmitting: false,
    showMoreOptions: false // 是否显示更多选项
  },

  onLoad() {
    // 获取现有名片信息
    if (app.globalData.cardInfo) {
      this.setData({
        formData: { ...app.globalData.cardInfo }
      })
    }
  },

  // 切换更多选项显示状态
  toggleMoreOptions() {
    this.setData({
      showMoreOptions: !this.data.showMoreOptions
    })
  },

  // 表单项变化处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail

    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 公司输入框变化处理
  onCompanyInput(e) {
    const value = e.detail.value
    this.setData({
      'formData.company': value
    })
  },

  // 公司选择处理
  onCompanySelect(e) {
    const value = e.detail.value
    const item = e.detail.item

    console.log('选择了企业:', value, item)

    this.setData({
      'formData.company': value
    })

    // 可以在这里添加其他逻辑，比如自动填充其他相关信息
  },

  // 选择器变化处理
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: this.data.industries[value]
    })
  },

  // 使用七牛云直传方式上传头像
  uploadAvatarDirectToQiniu() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        wx.showLoading({
          title: '上传中',
        });
        
        // 先显示临时图片
        this.setData({
          'formData.avatar': tempFilePath
        });
        
        // 先获取上传凭证
        wx.request({
          url: `${app.globalData.baseUrl}/upload/token`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('token')}`
          },
          success: (tokenRes) => {
            if (tokenRes.data && tokenRes.data.code === 0 && tokenRes.data.data) {
              const uploadInfo = tokenRes.data.data;
              const userId = app.globalData.userInfo ? app.globalData.userInfo.id : 'user';
              const timestamp = Date.now();
              const ext = tempFilePath.substring(tempFilePath.lastIndexOf('.'));
              const key = `${uploadInfo.pathPrefix}/${userId}_${timestamp}${ext}`;
              
              console.log('获取上传凭证成功，开始上传到七牛云');
              
              // 使用七牛云上传
              wx.uploadFile({
                url: uploadInfo.uploadUrl,
                filePath: tempFilePath,
                name: 'file',
                formData: {
                  'token': uploadInfo.token,
                  'key': key
                },
                timeout: 60000,
                success: (uploadRes) => {
                  let data;
                  try {
                    data = JSON.parse(uploadRes.data);
                    console.log('七牛云上传响应:', data);
                  } catch (e) {
                    console.error('解析上传响应失败:', e, uploadRes.data);
                    wx.showToast({
                      title: '头像上传失败',
                      icon: 'none'
                    });
                    return;
                  }
                  
                  if (data && data.key) {
                    const fileUrl = `${uploadInfo.domain}/${key}`;
                    console.log('上传成功，URL:', fileUrl);
                    
                    this.setData({
                      'formData.avatar': fileUrl
                    });
                    
                    wx.showToast({
                      title: '头像上传成功',
                      icon: 'success'
                    });
                  } else {
                    console.error('七牛云上传异常:', data);
                    wx.showToast({
                      title: '头像上传失败',
                      icon: 'none'
                    });
                    // 恢复原头像
                    this.setData({
                      'formData.avatar': app.globalData.cardInfo ? app.globalData.cardInfo.avatar : ''
                    });
                  }
                },
                fail: (err) => {
                  console.error('七牛云上传失败:', err);
                  wx.showToast({
                    title: '头像上传失败',
                    icon: 'none'
                  });
                  // 恢复原头像
                  this.setData({
                    'formData.avatar': app.globalData.cardInfo ? app.globalData.cardInfo.avatar : ''
                  });
                },
                complete: () => {
                  wx.hideLoading();
                }
              });
            } else {
              console.error('获取上传凭证失败:', tokenRes);
              wx.showToast({
                title: '系统错误，请重试',
                icon: 'none'
              });
              // 恢复原头像
              this.setData({
                'formData.avatar': app.globalData.cardInfo ? app.globalData.cardInfo.avatar : ''
              });
              wx.hideLoading();
            }
          },
          fail: (err) => {
            console.error('获取上传凭证请求失败:', err);
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
            // 恢复原头像
            this.setData({
              'formData.avatar': app.globalData.cardInfo ? app.globalData.cardInfo.avatar : ''
            });
            wx.hideLoading();
          }
        });
      }
    });
  },

  // 上传头像（重新实现，使用直传方式）
  uploadAvatar() {
    this.uploadAvatarDirectToQiniu();
  },

  // 获取微信头像（新版API）
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail;
    console.log('用户选择的头像URL:', avatarUrl);
    
    // 显示加载状态
    wx.showLoading({
      title: '处理头像中',
    });
    
    // 先显示临时图片
    this.setData({
      'formData.avatar': avatarUrl
    });
    
    // 上传到服务器（使用之前的七牛云上传方法）
    const tempFilePath = avatarUrl;
    
    // 先获取上传凭证
    wx.request({
      url: `${app.globalData.baseUrl}/upload/token`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (tokenRes) => {
        if (tokenRes.data && tokenRes.data.code === 0 && tokenRes.data.data) {
          const uploadInfo = tokenRes.data.data;
          const userId = app.globalData.userInfo ? app.globalData.userInfo.id : 'user';
          const timestamp = Date.now();
          const ext = '.jpg'; // 微信头像默认为jpg格式
          const key = `${uploadInfo.pathPrefix}/${userId}_${timestamp}${ext}`;
          
          console.log('获取上传凭证成功，开始上传到七牛云');
          
          // 使用七牛云上传
          wx.uploadFile({
            url: uploadInfo.uploadUrl,
            filePath: tempFilePath,
            name: 'file',
            formData: {
              'token': uploadInfo.token,
              'key': key
            },
            timeout: 60000,
            success: (uploadRes) => {
              let data;
              try {
                data = JSON.parse(uploadRes.data);
                console.log('七牛云上传响应:', data);
              } catch (e) {
                console.error('解析上传响应失败:', e, uploadRes.data);
                wx.showToast({
                  title: '头像上传失败',
                  icon: 'none'
                });
                return;
              }
              
              if (data && data.key) {
                const fileUrl = `${uploadInfo.domain}/${key}`;
                console.log('上传成功，URL:', fileUrl);
                
                this.setData({
                  'formData.avatar': fileUrl
                });
                
                wx.showToast({
                  title: '头像设置成功',
                  icon: 'success'
                });
              } else {
                console.error('七牛云上传异常:', data);
                wx.showToast({
                  title: '头像上传失败',
                  icon: 'none'
                });
                // 恢复原头像
                this.setData({
                  'formData.avatar': app.globalData.cardInfo ? app.globalData.cardInfo.avatar : ''
                });
              }
            },
            fail: (err) => {
              console.error('七牛云上传失败:', err);
              wx.showToast({
                title: '头像上传失败',
                icon: 'none'
              });
              // 恢复原头像
              this.setData({
                'formData.avatar': app.globalData.cardInfo ? app.globalData.cardInfo.avatar : ''
              });
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        } else {
          console.error('获取上传凭证失败:', tokenRes);
          wx.showToast({
            title: '系统错误，请重试',
            icon: 'none'
          });
          // 恢复原头像
          this.setData({
            'formData.avatar': app.globalData.cardInfo ? app.globalData.cardInfo.avatar : ''
          });
          wx.hideLoading();
        }
      },
      fail: (err) => {
        console.error('获取上传凭证请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        // 恢复原头像
        this.setData({
          'formData.avatar': app.globalData.cardInfo ? app.globalData.cardInfo.avatar : ''
        });
        wx.hideLoading();
      }
    });
  },

  // 表单验证
  validateForm() {
    const { formData, rules } = this.data
    let isValid = true
    let errorMessage = ''

    // 遍历验证规则
    for (const field in rules) {
      const fieldRules = rules[field]
      
      for (const rule of fieldRules) {
        if (rule.required && !formData[field]) {
          isValid = false
          errorMessage = rule.message
          break
        }
      }
      
      if (!isValid) break
    }

    if (!isValid) {
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }

    return isValid
  },

  // 提交表单
  submitForm() {
    if (this.data.isSubmitting) return
    
    if (!this.validateForm()) return
    
    this.setData({
      isSubmitting: true
    })
    
    wx.showLoading({
      title: '保存中',
    })
    
    const card = require('../../utils/card.js')
    card.saveCard(this.data.formData).then(res => {
      if (res.code === 0) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })
        // 设置标志表示名片已更新
        const app = getApp()
        app.globalData.cardUpdated = true
        app.globalData.cardInfo = this.data.formData
        
        // 设置上一个页面需要刷新的标志
        const pages = getCurrentPages()
        if (pages.length > 1) {
          const prevPage = pages[pages.length - 2]
          if (prevPage && prevPage.route === 'pages/personal/index') {
            prevPage.setData({
              needRefresh: true
            })
          }
        }
        
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('保存名片失败', err)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({
        isSubmitting: false
      })
      wx.hideLoading()
    })
  }
}) 