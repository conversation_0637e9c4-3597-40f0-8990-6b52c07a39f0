Page({
  data: {
    url: ''
  },

  onLoad(options) {
    console.log('webview页面加载，参数:', options);
    
    if (options.url) {
      const decodedUrl = decodeURIComponent(options.url);
      console.log('解码后的URL:', decodedUrl);
      
      this.setData({
        url: decodedUrl
      });
    } else {
      wx.showToast({
        title: '链接地址无效',
        icon: 'none'
      });
      
      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  onShow() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: 'AI分身'
    });
  },

  // webview加载完成
  onWebviewLoad(e) {
    console.log('webview加载完成:', e);
  },

  // webview加载失败
  onWebviewError(e) {
    console.error('webview加载失败:', e);
    wx.showToast({
      title: '页面加载失败',
      icon: 'none'
    });
  }
});
