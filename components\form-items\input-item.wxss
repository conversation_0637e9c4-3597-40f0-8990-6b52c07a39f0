.input-container {
  margin-bottom: 28rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 18rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.required {
  color: #ff4d4f;
  margin-right: 6rpx;
  font-size: 32rpx;
  line-height: 1;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: rgba(245, 247, 250, 0.8);
  border: 2rpx solid rgba(62, 127, 255, 0.1);
  border-radius: 12rpx;
  height: 88rpx;
  padding: 0 24rpx;
  transition: all 0.3s;
}

.input-wrapper:focus-within {
  border-color: rgba(62, 127, 255, 0.5);
  background-color: #ffffff;
  box-shadow: 0 4rpx 8rpx rgba(62, 127, 255, 0.1);
}

.input-control {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 30rpx;
  color: #333333;
}

.input-control::placeholder {
  color: #999999;
  font-size: 28rpx;
}

.clear-button {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM5OTkiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjE1IiB5MT0iOSIgeDI9IjkiIHkyPSIxNSI+PC9saW5lPjxsaW5lIHgxPSI5IiB5MT0iOSIgeDI9IjE1IiB5Mj0iMTUiPjwvbGluZT48L3N2Zz4=');
  background-size: cover;
  opacity: 0.6;
} 