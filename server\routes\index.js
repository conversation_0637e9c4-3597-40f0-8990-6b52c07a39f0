/**
 * 路由索引
 */
const express = require('express');
const router = express.Router();
const authRoutes = require('./auth.routes');
const cardRoutes = require('./card.routes');
const collectionRoutes = require('./collection.routes');
const companyRoutes = require('./company.routes');
const uploadRoutes = require('./upload.routes');
const productRoutes = require('./product.routes');

// 认证相关路由
router.use('/auth', authRoutes);

// 名片相关路由
router.use('/card', cardRoutes);

// 名片收藏相关路由
router.use('/collection', collectionRoutes);

// 企业相关路由
router.use('/company', companyRoutes);

// 文件上传相关路由
router.use('/upload', uploadRoutes);

// 产品相关路由
router.use('/products', productRoutes);

module.exports = router; 