.container {
  min-height: 100vh;
  background-color: #f8faff;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 40rpx;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-bottom: 1px solid #eaeaea;
  z-index: 100;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.back-btn {
  position: absolute;
  left: 30rpx;
  color: #3E7FFF;
  font-size: 30rpx;
}

.content {
  flex: 1;
  padding: 30rpx;
  padding-top: 110rpx;
  background-color: #fff;
  height: calc(100vh - 100rpx);
  box-sizing: border-box;
  width: 100%;
}

.section {
  margin-bottom: 40rpx;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  text-align: justify;
  word-wrap: break-word;
  word-break: break-all;
}

.list-item {
  padding-left: 20rpx;
  padding-right: 20rpx;
}

.footer {
  margin-top: 60rpx;
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding-bottom: 40rpx;
} 