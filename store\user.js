// store/user.js
const app = getApp();
const request = require('../utils/request.js');

/**
 * 获取用户信息
 * @returns {Promise} 用户信息请求Promise
 */
const getUserInfo = () => {
  return new Promise((resolve, reject) => {
    if (app.globalData.userInfo) {
      resolve({
        code: 0,
        message: 'success',
        data: app.globalData.userInfo
      });
      return;
    }

    request.get('/auth/user').then(res => {
      if (res.code === 0 && res.data) {
        app.globalData.userInfo = res.data;
      }
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 微信登录
 * @returns {Promise} 登录请求Promise
 */
const login = () => {
  return new Promise((resolve, reject) => {
    wx.login({
      success: loginRes => {
        if (loginRes.code) {
          request.post('/auth/login', {
            code: loginRes.code
          }).then(res => {
            if (res.code === 0 && res.data) {
              // 保存登录信息
              app.globalData.token = res.data.token;
              app.globalData.userInfo = {
                userId: res.data.userId,
                openid: res.data.openid,
                companyCode: res.data.companyCode,
                hasCardInfo: res.data.hasCardInfo
              };
              
              // 存储到本地
              wx.setStorageSync('token', res.data.token);
              wx.setStorageSync('userInfo', app.globalData.userInfo);
            }
            resolve(res);
          }).catch(err => {
            reject(err);
          });
        } else {
          reject(new Error('登录失败，未获取到code'));
        }
      },
      fail: err => {
        reject(err);
      }
    });
  });
};

/**
 * 退出登录
 */
const logout = () => {
  app.globalData.token = '';
  app.globalData.userInfo = null;
  app.globalData.cardInfo = null;
  wx.removeStorageSync('token');
  wx.removeStorageSync('userInfo');
};

module.exports = {
  getUserInfo,
  login,
  logout
}; 