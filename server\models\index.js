/**
 * 数据模型索引
 * 负责初始化Sequelize，建立数据库连接
 */
const { Sequelize } = require('sequelize');
const dbConfig = require('../config/db.config.js');

// 创建Sequelize实例
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    pool: dbConfig.pool,
    define: dbConfig.define,
    logging: dbConfig.logging,
    timezone: '+08:00'
  }
);

// 初始化数据库连接
const db = {};
db.Sequelize = Sequelize;
db.sequelize = sequelize;

// 导入模型
db.User = require('./user.model.js')(sequelize, Sequelize);
db.BusinessCard = require('./card.model.js')(sequelize, Sequelize);
db.CardCollection = require('./collection.model.js')(sequelize, Sequelize);
db.Company = require('./company.model.js')(sequelize, Sequelize);
db.Product = require('./product.model.js')(sequelize, Sequelize);

// 设置关联关系
// 用户与企业关联
db.User.belongsTo(db.Company, {
  foreignKey: 'company_code',
  targetKey: 'company_code',
  as: 'company'
});
db.Company.hasMany(db.User, {
  foreignKey: 'company_code',
  sourceKey: 'company_code',
  as: 'users'
});

// 用户与名片关联
db.User.hasOne(db.BusinessCard, {
  foreignKey: 'user_id',
  as: 'businessCard'
});
db.BusinessCard.belongsTo(db.User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 名片与企业关联
db.BusinessCard.belongsTo(db.Company, {
  foreignKey: 'company_code',
  targetKey: 'company_code',
  as: 'companyInfo'
});
db.Company.hasMany(db.BusinessCard, {
  foreignKey: 'company_code',
  sourceKey: 'company_code',
  as: 'businessCards'
});

// 用户与名片收藏关联
db.User.hasMany(db.CardCollection, {
  foreignKey: 'user_id',
  as: 'collections'
});
db.CardCollection.belongsTo(db.User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 名片与名片收藏关联
db.BusinessCard.hasMany(db.CardCollection, {
  foreignKey: 'card_id',
  as: 'collectors'
});
db.CardCollection.belongsTo(db.BusinessCard, {
  foreignKey: 'card_id',
  as: 'businessCard'
});

// 企业与产品关联
db.Company.hasMany(db.Product, {
  foreignKey: 'company_code',
  sourceKey: 'company_code',
  as: 'products'
});
db.Product.belongsTo(db.Company, {
  foreignKey: 'company_code',
  targetKey: 'company_code',
  as: 'company'
});

module.exports = db; 