<view class="container">
  <view class="header">
    <view class="header-left">
      <view class="title">名片夹</view>
      <view class="tip" wx:if="{{cardList.length > 0}}">（长按名片可删除）</view>
    </view>
    <view class="count">共{{total}}张名片</view>
  </view>
  
  <!-- 名片列表 -->
  <view class="card-list" wx:if="{{cardList.length > 0}}">
    <view
      class="card-item"
      wx:for="{{cardList}}"
      wx:key="id"
      data-id="{{item.id}}"
      data-collection-id="{{item.collectionId}}"
      data-index="{{index}}"
      bindtap="viewCardDetail"
      bindlongpress="onLongPress"
    >
      <view class="card-avatar">
        <image src="{{item.avatar || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-avatar.png'}}" mode="aspectFill"></image>
      </view>
      <view class="card-info">
        <view class="card-name">{{item.name}}</view>
        <view class="card-position">{{item.position}}</view>
        <view class="card-company">{{item.company}}</view>
      </view>
      <view class="card-time">
        {{item.collectedAt}}
      </view>
    </view>
  </view>
  
  <!-- 加载更多 -->
  <view class="loading-more" wx:if="{{isLoading && cardList.length > 0}}">
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
  </view>
  
  <!-- 已加载全部 -->
  <view class="no-more" wx:if="{{!hasMore && cardList.length > 0 && !isLoading}}">
    已加载全部
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && cardList.length === 0}}">
    <image class="empty-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/empty-folder.png" mode="aspectFit"></image>
    <view class="empty-text">您的名片夹还是空的</view>
    <view class="empty-subtext">收藏他人名片后可在此查看</view>
  </view>
</view> 