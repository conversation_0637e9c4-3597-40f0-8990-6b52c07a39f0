/**
 * 微信小程序接口工具
 */
const axios = require('axios');
const config = require('../config/app.config');

/**
 * 通过code获取微信小程序session
 * @param {string} code - 微信临时登录凭证
 * @returns {Promise<Object>} - 包含openid和session_key的对象
 */
exports.code2Session = async (code) => {
  try {
    const url = 'https://api.weixin.qq.com/sns/jscode2session';
    const params = {
      appid: config.weapp.appId,
      secret: config.weapp.appSecret,
      js_code: code,
      grant_type: 'authorization_code'
    };

    const response = await axios.get(url, { params });
    const data = response.data;

    if (data.errcode) {
      throw new Error(`微信API错误: ${data.errmsg}(${data.errcode})`);
    }

    return {
      openid: data.openid,
      session_key: data.session_key,
      unionid: data.unionid // 如果满足unionid下发条件，unionid会返回
    };
  } catch (error) {
    console.error('微信小程序登录接口错误:', error);
    throw error;
  }
};

/**
 * 解密微信用户信息
 * @param {string} encryptedData - 加密数据
 * @param {string} iv - 加密算法的初始向量
 * @param {string} sessionKey - 微信session_key
 * @returns {Promise<Object>} - 解密后的用户信息
 */
exports.decryptUserInfo = async (encryptedData, iv, sessionKey) => {
  // 微信小程序数据解密需要依赖额外的npm包，如crypto
  // 实际实现时需参考微信官方文档进行数据解密
  // https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/signature.html
  throw new Error('该功能未实现');
}; 