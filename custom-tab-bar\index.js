Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#3E7FFF",
    show: true, // 控制是否显示tabBar
    list: [{
      pagePath: "/pages/personal/index",
      iconPath: "/images/tab/personal.png",
      selectedIconPath: "/images/tab/personal-active.png",
      text: "个人"
    }, {
      pagePath: "/pages/personal/card-folder",
      iconPath: "/images/tab/card-folder.png",
      selectedIconPath: "/images/tab/card-folder-active.png",
      text: "名片夹"
    }],
    // 保留自定义名称字段，供名片详情页使用
    enterpriseName: "企业",
    productsName: "产品中心",
    hasCompanyTabs: false, // 始终为false，不显示企业和产品中心标签
    lastRequestTime: null
  },
  attached() {
    // 获取app实例
    const app = getApp();
    
    // 确保app.globalData中有tabBarControl对象
    if (!app.globalData.tabBarControl) {
      app.globalData.tabBarControl = {
        show: true,
        needCheckStatus: true
      };
    }
    
    // 初始化时检查一次状态
    this.checkLoginPage();
    
    // 获取企业自定义名称（仅用于名片详情页）
    this.getCustomNames();
    
    // 设置定时器，定期检查tabBar状态，解决显示不稳定问题
    this.checkInterval = setInterval(() => {
      // 仅当全局标记需要检查时才执行
      if (app.globalData.tabBarControl && app.globalData.tabBarControl.needCheckStatus) {
        this.checkLoginPage();
      }
    }, 2000); // 每2秒检查一次，减少检查频率
  },
  
  detached() {
    // 组件销毁时清除定时器
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }
  },
  
  methods: {
    // 获取企业自定义名称
    getCustomNames() {
      const app = getApp();
      
      // 防止频繁请求：如果上次请求时间距现在小于10秒，则不再请求
      const now = Date.now();
      if (this.lastRequestTime && now - this.lastRequestTime < 10000) {
        return;
      }
      this.lastRequestTime = now;
      
      // 导入公司状态管理模块
      const companyStore = require('../store/company.js');
      
      // 如果全局数据中已有企业信息，直接使用
      if (app.globalData.companyInfo) {
        const companyInfo = app.globalData.companyInfo;
        this.updateTabBarNames(
          companyInfo.enterpriseName || '企业',
          companyInfo.productsName || '产品中心'
        );
        return;
      }
      
      // 否则请求企业信息
      companyStore.getCompanyCustomNames().then(res => {
        if (res.code === 0 && res.data) {
          this.updateTabBarNames(
            res.data.enterpriseName || '企业',
            res.data.productsName || '产品中心'
          );
        }
      }).catch(err => {
        console.error('获取企业自定义名称失败', err);
      });
    },
    
    // 更新TabBar名称
    updateTabBarNames(enterpriseName, productsName) {
      // 更新数据，但不改变底部导航栏
      this.setData({
        enterpriseName: enterpriseName,
        productsName: productsName
      });
      
      // 始终保持只有个人和名片夹两个选项
      const basicList = [{
        pagePath: "/pages/personal/index",
        iconPath: "/images/tab/personal.png",
        selectedIconPath: "/images/tab/personal-active.png",
        text: "个人"
      }, {
        pagePath: "/pages/personal/card-folder",
        iconPath: "/images/tab/card-folder.png",
        selectedIconPath: "/images/tab/card-folder-active.png",
        text: "名片夹"
      }];
      
      this.setData({
        list: basicList,
        hasCompanyTabs: false
      });
      
      // 更新选中状态
      this.setData({
        selected: this.getTabBarIndex()
      });
    },
    
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      
      // 立即更新选中状态，不等待页面跳转完成
      this.setData({
        selected: data.index
      });
      
      // 跳转到对应页面
      wx.switchTab({
        url
      });
    },
    
    getTabBarIndex() {
      // 获取当前页面路径
      const pages = getCurrentPages();
      // 检查 pages 数组是否有内容
      if (!pages || pages.length === 0) {
        return 0; // 默认返回第一个标签页（个人页面）
      }
      
      const currentPage = pages[pages.length - 1];
      // 检查 currentPage 是否存在及是否有 route 属性
      if (!currentPage || !currentPage.route) {
        return 0; // 默认返回第一个标签页
      }
      
      const url = `/${currentPage.route}`;
      
      // 找出当前页面对应的tabBar索引
      const tabList = this.data.list;
      for (let i = 0; i < tabList.length; i++) {
        if (tabList[i].pagePath === url) {
          return i;
        }
      }
      return 0;
    },
    
    // 检查当前是否是登录页面
    checkLoginPage() {
      const app = getApp();
      const pages = getCurrentPages();
      
      if (!pages || pages.length === 0) {
        return;
      }
      
      const currentPage = pages[pages.length - 1];
      if (!currentPage || !currentPage.route) {
        return;
      }
      
      const url = `/${currentPage.route}`;
      
      // 优先使用全局tabBar控制状态 - 如果明确设置为false，则一定隐藏
      if (app.globalData.tabBarControl && app.globalData.tabBarControl.show === false) {
        if (this.data.show !== false) {
          this.setData({
            show: false
          });
        }
        return; // 立即返回，不再继续检查
      }
      
      // 检查是否应该显示TabBar - 修改为默认显示，除了登录页面外
      let shouldShowTabBar = true;
      
      // 只有登录页面不显示TabBar
      if (url === '/pages/login/index') {
        shouldShowTabBar = false;
      }
      
      // 只有当状态变化时才更新
      if (this.data.show !== shouldShowTabBar) {
        this.setData({
          show: shouldShowTabBar
        });
        
        // 同步到全局状态
        if (app.globalData.tabBarControl) {
          app.globalData.tabBarControl.show = shouldShowTabBar;
        }
        
        // 在状态变化时打印日志，便于调试
        console.log(`TabBar显示状态更新: ${shouldShowTabBar ? '显示' : '隐藏'}, 页面: ${url}`);
        
        // 仅在TabBar状态变化且显示时获取自定义名称
        if (shouldShowTabBar && app.globalData.userInfo && app.globalData.userInfo.companyCode) {
          this.getCustomNames();
        }
      }
    },
    
    // 提供一个手动设置TabBar显示状态的方法
    setTabBarShow(show, needCheck = true) {
      const app = getApp();
      
      // 更新组件状态
      this.setData({
        show: show
      });
      
      // 更新全局状态
      if (app.globalData.tabBarControl) {
        app.globalData.tabBarControl.show = show;
        app.globalData.tabBarControl.needCheckStatus = needCheck;
      }
    },
    
    // 提供一个手动更新TabBar名称的方法
    updateNames(enterpriseName, productsName) {
      this.updateTabBarNames(enterpriseName, productsName);
    }
  }
}) 