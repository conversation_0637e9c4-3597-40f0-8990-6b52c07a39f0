<view class="upload-container">
  <view class="upload-label">
    <text class="required" wx:if="{{required}}">*</text>
    <text>{{label}}</text>
  </view>
  
  <view class="upload-wrapper">
    <!-- 上传按钮 -->
    <view class="upload-button" bindtap="onChooseImage" wx:if="{{!imageUrl && !isLoading}}">
      <view class="upload-icon"></view>
      <text class="upload-text">{{uploadText}}</text>
    </view>
    
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-icon"></view>
      <text class="loading-text">上传中...</text>
    </view>
    
    <!-- 已上传图片 -->
    <view class="image-container" wx:if="{{imageUrl && !isLoading}}">
      <image class="uploaded-image" src="{{imageUrl}}" mode="aspectFill" bindtap="onPreviewImage"></image>
      <view class="image-actions">
        <view class="action-button preview-button" bindtap="onPreviewImage">
          <view class="preview-icon"></view>
        </view>
        <view class="action-button delete-button" bindtap="onDeleteImage">
          <view class="delete-icon"></view>
        </view>
      </view>
    </view>
  </view>
</view> 