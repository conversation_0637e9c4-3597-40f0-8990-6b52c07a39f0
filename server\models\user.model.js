/**
 * 用户模型
 */
module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    openid: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '微信openid'
    },
    unionid: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '微信unionid'
    },
    session_key: {
      type: DataTypes.STRING(128),
      allowNull: true,
      comment: '微信会话密钥'
    },
    company_code: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '企业唯一标识码'
    },
    ai_avatar_url: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'AI分身URL'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'users',
    timestamps: true
  });

  return User;
}; 