-- 创建智链应用数据库
CREATE DATABASE IF NOT EXISTS zhilian CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE zhilian;

-- 企业表
CREATE TABLE IF NOT EXISTS companies (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '企业唯一标识码',
  name VARCHAR(100) NOT NULL COMMENT '企业名称',
  enterprise_page VARCHAR(255) COMMENT '企业页面路径',
  products_page VARCHAR(255) COMMENT '产品中心页面路径',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_company_code (company_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业表';

-- 添加企业自定义名称和分享图片字段
ALTER TABLE companies
ADD COLUMN enterprise_name VARCHAR(30) DEFAULT '企业' COMMENT '自定义企业页面名称',
ADD COLUMN products_name VARCHAR(30) DEFAULT '产品中心' COMMENT '自定义产品中心名称',
ADD COLUMN enterprise_share_image VARCHAR(255) COMMENT '企业页面分享图URL',
ADD COLUMN products_share_image VARCHAR(255) COMMENT '产品中心分享图URL';

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  openid VARCHAR(50) NOT NULL UNIQUE COMMENT '微信openid',
  unionid VARCHAR(50) COMMENT '微信unionid',
  session_key VARCHAR(128) COMMENT '微信会话密钥',
  company_code VARCHAR(50) COMMENT '企业唯一标识码',
  ai_avatar_url VARCHAR(255) COMMENT 'AI分身URL',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_openid (openid),
  INDEX idx_company_code (company_code),
  FOREIGN KEY (company_code) REFERENCES companies(company_code) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 名片表
CREATE TABLE IF NOT EXISTS business_cards (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '关联用户ID',
  avatar VARCHAR(255) COMMENT '头像URL',
  name VARCHAR(50) NOT NULL COMMENT '姓名',
  company VARCHAR(100) NOT NULL COMMENT '公司',
  position VARCHAR(50) COMMENT '职位',
  industry VARCHAR(50) COMMENT '行业',
  mobile VARCHAR(20) COMMENT '手机号',
  wechat VARCHAR(50) COMMENT '微信号',
  email VARCHAR(100) COMMENT '邮箱',
  phone VARCHAR(20) COMMENT '座机',
  website VARCHAR(255) COMMENT '网址',
  address VARCHAR(255) COMMENT '地址',
  introduction TEXT COMMENT '业务简介',
  company_code VARCHAR(50) COMMENT '企业唯一标识码',
  is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认名片',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (company_code) REFERENCES companies(company_code) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_company_code (company_code),
  INDEX idx_is_default (is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片表';

-- 名片夹表(名片收藏关系)
CREATE TABLE IF NOT EXISTS card_collections (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '收藏者用户ID',
  card_id INT NOT NULL COMMENT '被收藏的名片ID',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (card_id) REFERENCES business_cards(id) ON DELETE CASCADE,
  UNIQUE KEY uk_user_card (user_id, card_id) COMMENT '防止重复收藏',
  INDEX idx_user_id (user_id),
  INDEX idx_card_id (card_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片收藏关系表';

-- 创建产品表
CREATE TABLE IF NOT EXISTS products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(100) NOT NULL COMMENT '产品标题',
  description TEXT COMMENT '产品描述',
  cover_image VARCHAR(255) COMMENT '产品封面图URL',
  content TEXT COMMENT '产品详细内容',
  share_image VARCHAR(255) COMMENT '产品分享图URL',
  share_title VARCHAR(100) COMMENT '产品分享标题',
  company_code VARCHAR(50) NOT NULL COMMENT '企业唯一标识码',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (company_code) REFERENCES companies(company_code) ON DELETE CASCADE,
  INDEX idx_company_code (company_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- 添加企业和产品备注：企业页面和产品中心页面使用小程序内部页面路径，在companies表中直接存储对应路径

-- 企业搜索功能说明：
-- 1. companies表的name字段用于企业名称的模糊搜索
-- 2. 搜索使用LIKE查询，支持部分匹配
-- 3. 搜索结果按以下规则排序：
--    - 优先显示以关键词开头的企业（使用CASE WHEN name LIKE 'keyword%'）
--    - 其次按企业名称字母顺序排序
-- 4. 限制搜索结果最多返回10条记录
-- 5. 搜索接口无需登录验证，支持公开访问

-- AI分身功能说明：
-- 1. users表的ai_avatar_url字段存储用户的AI分身页面URL
-- 2. 字段类型：VARCHAR(255)，可空
-- 3. 功能实现：
--    - 个人视角：在个人名片页面的企业按钮左边显示AI分身按钮
--    - 他人视角：在分享名片详情页面的分享按钮右边显示AI分身按钮
--    - 显示逻辑：只有当用户有AI分身URL时才显示按钮
--    - 跳转逻辑：点击按钮通过webview页面加载外部AI分身URL
-- 4. 数据获取：通过用户认证接口和名片信息接口返回aiAvatarUrl字段
-- 5. 安全考虑：URL需要是有效的HTTPS链接，支持在微信小程序webview中加载