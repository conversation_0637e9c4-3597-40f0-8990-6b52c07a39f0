.card-list-container {
  width: 100%;
  min-height: 300rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3E7FFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 50rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 30rpx;
  color: #999999;
  text-align: center;
}

/* 名片列表 */
.card-list {
  padding: 20rpx;
}

.card-item {
  margin-bottom: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(62, 127, 255, 0.08);
  padding: 30rpx;
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  transition: all 0.3s;
}

.card-item:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 卡片背景装饰 */
.card-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, rgba(62, 127, 255, 0.03) 0%, rgba(62, 127, 255, 0.08) 100%);
  border-radius: 0 0 0 180rpx;
  z-index: 0;
}

.card-content {
  display: flex;
  position: relative;
  z-index: 1;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
  border: 3rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(62, 127, 255, 0.15);
  flex-shrink: 0;
}

.card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

.name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.name {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.industry {
  font-size: 22rpx;
  color: #3E7FFF;
  background-color: rgba(62, 127, 255, 0.08);
  padding: 2rpx 12rpx;
  border-radius: 20rpx;
}

.position, .company {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contact {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999;
}

.contact-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.phone-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiMzRTdGRkYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJNMjIgMTYuOTJWMTljMCAyLjIxLTEuNzggNC00IDEuMjEgMS4yMSAyLjkzIDEuMiA0LjE1LjMyIDUuMzUtLjg5IDExLjE5LTcuMDYgMTIuMzgtOC4zNyA1LjMyLTEuOTkgOS45Mi01LjA2IDEwLjctNi42NC41MS0xLjU4IDEuMy0yLjUyIDAuMTUtMy40OS0xLjE3QTUgNSAwIDAgMSAxVjYuMDhBMSAxIDAgMCAxIDIuMjUgNWMyLjI3LjA2IDUuNDcgMS4wNSA3LjU3IDMuMTUgMi4xIDIuMS0uMTUgMS44LTEuMTQgMy42N2MtLjk5IDEuODYgMi43MSA0LjY3IDQuNjkgNC42N3YxLjQzeiI+PC9wYXRoPjwvc3ZnPg==');
  background-size: cover;
}

.add-time {
  font-size: 22rpx;
  color: #999999;
  text-align: right;
  margin-top: 20rpx;
}

.card-delete {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM5OTkiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cG9seWxpbmUgcG9pbnRzPSIzIDYgNSA2IDIxIDYiPjwvcG9seWxpbmU+PHBhdGggZD0iTTE5IDZ2MTRhMiAyIDAgMCAxLTIgMkg3YTIgMiAwIDAgMS0yLTJWNm0zIDBWNGEyIDIgMCAwIDEgMi0yaDRhMiAyIDAgMCAxIDIgMnYyIj48L3BhdGg+PGxpbmUgeDE9IjEwIiB5MT0iMTEiIHgyPSIxMCIgeTI9IjE3Ij48L2xpbmU+PGxpbmUgeDE9IjE0IiB5MT0iMTEiIHgyPSIxNCIgeTI9IjE3Ij48L2xpbmU+PC9zdmc+');
  background-size: cover;
} 