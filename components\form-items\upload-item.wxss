.upload-container {
  margin-bottom: 28rpx;
}

.upload-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 18rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.required {
  color: #ff4d4f;
  margin-right: 6rpx;
  font-size: 32rpx;
  line-height: 1;
}

.upload-wrapper {
  display: flex;
  justify-content: center;
}

/* 上传按钮 */
.upload-button {
  width: 200rpx;
  height: 200rpx;
  background-color: rgba(245, 247, 250, 0.8);
  border: 2rpx solid rgba(62, 127, 255, 0.2);
  border-radius: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.3s;
  overflow: hidden;
}

.upload-button:active {
  background-color: rgba(62, 127, 255, 0.1);
  border-color: rgba(62, 127, 255, 0.3);
}

.upload-icon {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 16rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiMzRTdGRkYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCI+PC9wYXRoPjxwb2x5bGluZSBwb2ludHM9IjE3IDggMTIgMyA3IDgiPjwvcG9seWxpbmU+PGxpbmUgeDE9IjEyIiB5MT0iMyIgeDI9IjEyIiB5Mj0iMTUiPjwvbGluZT48L3N2Zz4=');
  background-size: cover;
}

.upload-text {
  font-size: 26rpx;
  color: #3E7FFF;
}

/* 加载中 */
.loading-container {
  width: 200rpx;
  height: 200rpx;
  background-color: rgba(245, 247, 250, 0.8);
  border: 2rpx solid rgba(62, 127, 255, 0.2);
  border-radius: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.loading-icon {
  width: 56rpx;
  height: 56rpx;
  border: 4rpx solid rgba(62, 127, 255, 0.2);
  border-top: 4rpx solid #3E7FFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #3E7FFF;
}

/* 已上传图片 */
.image-container {
  width: 200rpx;
  height: 200rpx;
  position: relative;
  border-radius: 100rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(62, 127, 255, 0.1);
  border: 4rpx solid #ffffff;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.action-button {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-icon {
  width: 36rpx;
  height: 36rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJNMTQgMnY0YTIgMiAwIDAgMCAyIDJoNCI+PC9wYXRoPjxwYXRoIGQ9Ik0yMiAxNlYxOGEyIDIgMCAwIDEtMiAySDRhMiAyIDAgMCAxLTItMlY2YTIgMiAwIDAgMSAyLTJoMTJ6Ij48L3BhdGg+PC9zdmc+');
  background-size: cover;
}

.delete-icon {
  width: 36rpx;
  height: 36rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cG9seWxpbmUgcG9pbnRzPSIzIDYgNSA2IDIxIDYiPjwvcG9seWxpbmU+PHBhdGggZD0iTTE5IDZ2MTRhMiAyIDAgMCAxLTIgMkg3YTIgMiAwIDAgMS0yLTJWNm0zIDBWNGEyIDIgMCAwIDEgMi0yaDRhMiAyIDAgMCAxIDIgMnYyIj48L3BhdGg+PGxpbmUgeDE9IjEwIiB5MT0iMTEiIHgyPSIxMCIgeTI9IjE3Ij48L2xpbmU+PGxpbmUgeDE9IjE0IiB5MT0iMTEiIHgyPSIxNCIgeTI9IjE3Ij48L2xpbmU+PC9zdmc+');
  background-size: cover;
} 