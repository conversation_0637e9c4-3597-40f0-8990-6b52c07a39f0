const qiniu = require('qiniu');
const config = require('../config/app.config');

// 创建鉴权对象
const mac = new qiniu.auth.digest.Mac(config.qiniu.accessKey, config.qiniu.secretKey);

// 检查七牛云配置
const checkQiniuConfig = () => {
  const { accessKey, secretKey, bucket, domain } = config.qiniu;
  
  if (!accessKey) {
    throw new Error('七牛云配置错误: 缺少AccessKey');
  }
  
  if (!secretKey) {
    throw new Error('七牛云配置错误: 缺少SecretKey');
  }
  
  if (!bucket) {
    throw new Error('七牛云配置错误: 缺少Bucket');
  }
  
  if (!domain) {
    throw new Error('七牛云配置错误: 缺少Domain');
  }
  
  return true;
};

// 生成上传凭证
const generateUploadToken = () => {
  try {
    // 检查配置
    checkQiniuConfig();
    
    console.log(`为存储空间 ${config.qiniu.bucket} 生成上传凭证`);
    const putPolicy = new qiniu.rs.PutPolicy({
      scope: config.qiniu.bucket,
      expires: 7200 // 2小时有效期
    });
    
    const token = putPolicy.uploadToken(mac);
    console.log('上传凭证生成成功');
    return token;
  } catch (err) {
    console.error('生成上传凭证出错:', err);
    throw err;
  }
};

// 上传文件到七牛云
const uploadToQiniu = async (fileBuffer, key) => {
  if (!fileBuffer || fileBuffer.length === 0) {
    console.error('上传内容为空');
    throw new Error('上传内容不能为空');
  }

  console.log(`准备上传文件到七牛云, key: ${key}, 文件大小: ${fileBuffer.length}字节`);
  
  try {
    // 检查配置
    checkQiniuConfig();
    
    const uploadToken = generateUploadToken();
    console.log('已获取上传凭证');
    
    // 配置上传选项
    const config = new qiniu.conf.Config();
    // 空间对应的机房
    config.zone = qiniu.zone.z0; // 华东机房，可根据实际情况修改
    // 设置超时时间
    config.uphost = 'http://up.qiniup.com';
    config.useHttpsDomain = true;
    config.useCdnDomain = true;
    config.timeout = 60000; // 60秒超时
    
    const formUploader = new qiniu.form_up.FormUploader(config);
    const putExtra = new qiniu.form_up.PutExtra();

    // 构建完整的key
    const fullKey = `${config.qiniu.pathPrefix}/${key}`;
    console.log(`完整的存储路径: ${fullKey}`);

    return new Promise((resolve, reject) => {
      console.log('开始执行上传操作...');
      formUploader.put(uploadToken, fullKey, fileBuffer, putExtra, (err, respBody, respInfo) => {
        if (err) {
          console.error('七牛云上传过程报错:', err);
          reject(err);
          return;
        }
        
        console.log(`七牛云上传响应状态码: ${respInfo.statusCode}`);
        console.log('七牛云上传响应体:', respBody);
        
        if (respInfo.statusCode === 200) {
          // 构建完整URL
          const fileUrl = `${config.qiniu.domain}/${config.qiniu.pathPrefix}/${key}`;
          console.log(`文件上传成功，访问URL: ${fileUrl}`);
          
          resolve({
            key: respBody.key,
            url: fileUrl
          });
        } else {
          console.error('七牛云上传失败:', respBody);
          reject(new Error(`上传失败: ${respBody.error || '未知错误'}`));
        }
      });
    });
  } catch (error) {
    console.error('上传到七牛云错误:', error);
    throw error;
  }
};

// 尝试使用直接上传URL方式
const getDirectUploadUrl = () => {
  checkQiniuConfig();
  const uploadToken = generateUploadToken();
  
  return {
    token: uploadToken,
    domain: config.qiniu.domain,
    pathPrefix: config.qiniu.pathPrefix,
    uploadUrl: 'https://up.qiniup.com' // 七牛云华东区上传域名
  };
};

module.exports = {
  generateUploadToken,
  uploadToQiniu,
  getDirectUploadUrl,
  checkQiniuConfig
}; 