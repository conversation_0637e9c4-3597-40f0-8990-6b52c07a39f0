/**
 * 企业控制器
 */
const db = require('../models');
const { generateResponse } = require('../utils/common');
const { ApiError } = require('../middlewares/error.middleware');

const Company = db.Company;
const User = db.User;

/**
 * 获取企业信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.getCompanyInfo = async (req, res, next) => {
  try {
    // 检查是否通过查询参数指定了company_code
    let companyCode = req.query.companyCode;
    
    // 如果没有通过查询参数指定，则从当前用户获取
    if (!companyCode) {
      // 从用户获取company_code
      const user = await User.findByPk(req.userId);
      
      if (!user) {
        throw new ApiError(404, '用户不存在');
      }

      // 如果用户未关联企业
      if (!user.company_code) {
        return res.json(generateResponse(0, 'success', null));
      }
      
      companyCode = user.company_code;
    }

    // 查询企业信息
    const company = await Company.findOne({
      where: { company_code: companyCode }
    });

    if (!company) {
      return res.json(generateResponse(0, 'success', null));
    }

    // 返回企业信息，添加自定义名称字段
    return res.json(generateResponse(0, 'success', {
      companyCode: company.company_code,
      name: company.name,
      enterprisePage: company.enterprise_page,
      productsPage: company.products_page,
      enterpriseName: company.enterprise_name || '企业', // 添加自定义企业名称，默认为"企业"
      productsName: company.products_name || '产品中心', // 添加自定义产品中心名称，默认为"产品中心"
      enterpriseShareImage: company.enterprise_share_image, // 添加企业分享图片URL
      productsShareImage: company.products_share_image // 添加产品中心分享图片URL
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 更新企业页面路径
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.updateCompanyPages = async (req, res, next) => {
  try {
    const { companyCode, enterprisePage, productsPage } = req.body;
    
    if (!companyCode) {
      throw new ApiError(400, '缺少必要参数：companyCode');
    }

    // 查找企业
    const company = await Company.findOne({
      where: { company_code: companyCode }
    });
    
    if (!company) {
      throw new ApiError(404, '企业不存在');
    }

    // 更新页面路径
    await company.update({
      enterprise_page: enterprisePage,
      products_page: productsPage
    });

    // 返回更新后的企业信息
    return res.json(generateResponse(0, 'success', {
      companyCode: company.company_code,
      enterprisePage: company.enterprise_page,
      productsPage: company.products_page,
      updatedAt: company.updated_at
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 更新企业自定义名称
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.updateCustomNames = async (req, res, next) => {
  try {
    const { enterpriseName, productsName } = req.body;
    let { companyCode } = req.body;
    
    if (!companyCode) {
      // 如果没有指定companyCode，则从当前用户获取
      const user = await User.findByPk(req.userId);
      
      if (!user || !user.company_code) {
        throw new ApiError(400, '未关联企业或缺少必要参数：companyCode');
      }
      
      companyCode = user.company_code;
    }

    // 查找企业
    const company = await Company.findOne({
      where: { company_code: companyCode }
    });
    
    if (!company) {
      throw new ApiError(404, '企业不存在');
    }

    // 更新自定义名称，确保名称长度不超过30个字符
    const updateData = {};
    if (enterpriseName !== undefined) {
      updateData.enterprise_name = enterpriseName.slice(0, 30);
    }
    if (productsName !== undefined) {
      updateData.products_name = productsName.slice(0, 30);
    }

    // 只有在有数据需要更新时才进行更新
    if (Object.keys(updateData).length > 0) {
      await company.update(updateData);
    }

    // 返回更新后的企业信息
    return res.json(generateResponse(0, 'success', {
      companyCode: company.company_code,
      enterpriseName: company.enterprise_name || '企业',
      productsName: company.products_name || '产品中心',
      updatedAt: company.updated_at
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 更新企业分享图片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.updateShareImages = async (req, res, next) => {
  try {
    const { companyCode, enterpriseShareImage, productsShareImage } = req.body;

    if (!companyCode) {
      throw new ApiError(400, '缺少必要参数：companyCode');
    }

    // 查找企业
    const company = await Company.findOne({
      where: { company_code: companyCode }
    });

    if (!company) {
      throw new ApiError(404, '企业不存在');
    }

    // 更新分享图片URL
    const updateData = {};
    if (enterpriseShareImage !== undefined) {
      updateData.enterprise_share_image = enterpriseShareImage;
    }
    if (productsShareImage !== undefined) {
      updateData.products_share_image = productsShareImage;
    }

    // 只有在有数据需要更新时才进行更新
    if (Object.keys(updateData).length > 0) {
      await company.update(updateData);
    }

    // 返回更新后的企业信息
    return res.json(generateResponse(0, 'success', {
      companyCode: company.company_code,
      enterpriseShareImage: company.enterprise_share_image,
      productsShareImage: company.products_share_image,
      updatedAt: company.updated_at
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 搜索企业名称
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.searchCompanies = async (req, res, next) => {
  try {
    const { keyword } = req.query;
    console.log('搜索企业，关键词:', keyword);

    if (!keyword || keyword.trim().length === 0) {
      console.log('关键词为空，返回空数组');
      return res.json(generateResponse(0, 'success', []));
    }

    // 使用LIKE进行模糊搜索，限制返回结果数量
    const { Op } = require('sequelize');
    const companies = await Company.findAll({
      where: {
        name: {
          [Op.like]: `%${keyword.trim()}%`
        }
      },
      attributes: ['name', 'company_code'],
      limit: 10, // 限制返回最多10个结果
      order: [
        // 优先显示以关键词开头的企业
        [db.sequelize.literal(`CASE WHEN name LIKE '${keyword.trim()}%' THEN 0 ELSE 1 END`), 'ASC'],
        ['name', 'ASC']
      ]
    });

    console.log(`找到 ${companies.length} 个匹配的企业:`, companies.map(c => c.name));

    // 返回企业名称列表
    const results = companies.map(company => ({
      name: company.name,
      companyCode: company.company_code
    }));

    console.log('返回结果:', results);
    return res.json(generateResponse(0, 'success', results));
  } catch (error) {
    console.error('搜索企业出错:', error);
    next(error);
  }
};