.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-left {
  display: flex;
  align-items: center;
}

.count {
  font-size: 28rpx;
  color: #999;
}

.tip {
  font-size: 24rpx;
  color: #999;
}

/* 名片列表样式 */
.card-list {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.card-item:last-child {
  border-bottom: none;
}

.card-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.card-avatar image {
  width: 100%;
  height: 100%;
}

.card-info {
  flex: 1;
  overflow: hidden;
}

.card-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-position {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-company {
  font-size: 26rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-time {
  font-size: 24rpx;
  color: #999;
  position: absolute;
  right: 30rpx;
  top: 30rpx;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100rpx;
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #3E7FFF;
  margin: 0 8rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 26rpx;
  padding: 30rpx 0;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #999;
} 