/**
 * 产品控制器
 */
const db = require('../models');
const { generateResponse } = require('../utils/common');
const { ApiError } = require('../middlewares/error.middleware');

const Product = db.Product;
const Company = db.Company;
const User = db.User;

/**
 * 获取企业产品列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.getProducts = async (req, res, next) => {
  try {
    // 检查是否通过查询参数指定了company_code
    let companyCode = req.query.companyCode;
    
    // 如果没有通过查询参数指定，则从当前用户获取
    if (!companyCode && req.userId) {
      // 从用户获取company_code
      const user = await User.findByPk(req.userId);
      
      if (!user) {
        throw new ApiError(404, '用户不存在');
      }

      // 如果用户未关联企业
      if (!user.company_code) {
        return res.json(generateResponse(0, 'success', { list: [] }));
      }
      
      companyCode = user.company_code;
    }

    if (!companyCode) {
      throw new ApiError(400, '缺少必要参数：companyCode');
    }

    // 查询企业是否存在
    const company = await Company.findOne({
      where: { company_code: companyCode }
    });

    if (!company) {
      return res.json(generateResponse(0, 'success', { list: [] }));
    }

    // 查询企业的所有产品，按display_order排序
    const products = await Product.findAll({
      where: { company_code: companyCode },
      order: [['display_order', 'ASC'], ['id', 'ASC']],
      attributes: ['id', 'name', 'description', 'cover_image', 'product_url', 'share_image', 'display_order', 'created_at', 'updated_at']
    });

    // 返回产品列表
    return res.json(generateResponse(0, 'success', {
      list: products.map(product => ({
        id: product.id,
        name: product.name,
        description: product.description,
        coverImage: product.cover_image,
        productUrl: product.product_url,
        shareImage: product.share_image,
        displayOrder: product.display_order,
        createdAt: product.created_at,
        updatedAt: product.updated_at
      }))
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 获取产品详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.getProductById = async (req, res, next) => {
  try {
    const productId = req.params.id;
    
    // 查询产品
    const product = await Product.findByPk(productId);
    
    if (!product) {
      throw new ApiError(404, '产品不存在');
    }

    // 返回产品详情
    return res.json(generateResponse(0, 'success', {
      id: product.id,
      name: product.name,
      description: product.description,
      coverImage: product.cover_image,
      productUrl: product.product_url,
      shareImage: product.share_image,
      displayOrder: product.display_order,
      companyCode: product.company_code,
      createdAt: product.created_at,
      updatedAt: product.updated_at
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 创建产品
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.createProduct = async (req, res, next) => {
  try {
    const { companyCode, name, description, coverImage, productUrl, shareImage } = req.body;
    
    if (!companyCode || !name) {
      throw new ApiError(400, '缺少必要参数：companyCode, name');
    }

    // 查询企业是否存在
    const company = await Company.findOne({
      where: { company_code: companyCode }
    });

    if (!company) {
      throw new ApiError(404, '企业不存在');
    }

    // 获取当前最大的display_order
    const maxOrderProduct = await Product.findOne({
      where: { company_code: companyCode },
      order: [['display_order', 'DESC']]
    });

    const displayOrder = maxOrderProduct ? maxOrderProduct.display_order + 1 : 0;

    // 创建产品
    const product = await Product.create({
      company_code: companyCode,
      name,
      description,
      cover_image: coverImage,
      product_url: productUrl,
      share_image: shareImage,
      display_order: displayOrder
    });

    // 返回创建的产品
    return res.json(generateResponse(0, 'success', {
      id: product.id,
      name: product.name,
      description: product.description,
      coverImage: product.cover_image,
      productUrl: product.product_url,
      shareImage: product.share_image,
      displayOrder: product.display_order,
      companyCode: product.company_code,
      createdAt: product.created_at,
      updatedAt: product.updated_at
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 更新产品
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.updateProduct = async (req, res, next) => {
  try {
    const productId = req.params.id;
    const { name, description, coverImage, productUrl, shareImage } = req.body;
    
    // 查询产品
    const product = await Product.findByPk(productId);
    
    if (!product) {
      throw new ApiError(404, '产品不存在');
    }

    // 更新产品
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (coverImage !== undefined) updateData.cover_image = coverImage;
    if (productUrl !== undefined) updateData.product_url = productUrl;
    if (shareImage !== undefined) updateData.share_image = shareImage;

    // 只有在有数据需要更新时才进行更新
    if (Object.keys(updateData).length > 0) {
      await product.update(updateData);
    }

    // 返回更新后的产品
    return res.json(generateResponse(0, 'success', {
      id: product.id,
      name: product.name,
      description: product.description,
      coverImage: product.cover_image,
      productUrl: product.product_url,
      shareImage: product.share_image,
      displayOrder: product.display_order,
      companyCode: product.company_code,
      createdAt: product.created_at,
      updatedAt: product.updated_at
    }));
  } catch (error) {
    next(error);
  }
};

/**
 * 删除产品
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.deleteProduct = async (req, res, next) => {
  try {
    const productId = req.params.id;
    
    // 查询产品
    const product = await Product.findByPk(productId);
    
    if (!product) {
      throw new ApiError(404, '产品不存在');
    }

    // 删除产品
    await product.destroy();

    // 返回成功响应
    return res.json(generateResponse(0, 'success', null));
  } catch (error) {
    next(error);
  }
};

/**
 * 调整产品顺序
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
exports.updateProductOrder = async (req, res, next) => {
  try {
    const { productIds } = req.body;
    
    if (!productIds || !Array.isArray(productIds)) {
      throw new ApiError(400, '缺少必要参数：productIds');
    }

    // 开始事务
    const transaction = await db.sequelize.transaction();

    try {
      // 更新每个产品的顺序
      for (let i = 0; i < productIds.length; i++) {
        await Product.update(
          { display_order: i },
          { 
            where: { id: productIds[i] },
            transaction
          }
        );
      }

      // 提交事务
      await transaction.commit();

      // 返回成功响应
      return res.json(generateResponse(0, 'success', { order: productIds }));
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
}; 