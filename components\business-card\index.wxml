<view class="card-container">
  <view class="card-body">
    <!-- 基本信息 -->
    <view class="card-header">
      <view class="basic-info">
        <view class="name">{{cardInfo.name || '未填写姓名'}}</view>
        <view class="company">{{cardInfo.company || '未填写公司'}}</view>
        <view class="position" wx:if="{{cardInfo.position}}">{{cardInfo.position}}</view>
      </view>
      <image class="avatar" src="{{cardInfo.avatar || defaultAvatar}}" mode="aspectFill"></image>
    </view>
    
    <!-- 联系方式 -->
    <view class="contact-section">
      <view class="contact-item" bindtap="onCall" wx:if="{{cardInfo.mobile}}">
        <image class="contact-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/phone.png" mode="aspectFit"></image>
        <view class="contact-value">{{cardInfo.mobile}}</view>
      </view>
      <view class="contact-item" bindtap="onCopyWechat" wx:if="{{cardInfo.wechat}}">
        <image class="contact-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/wechat.png" mode="aspectFit"></image>
        <view class="contact-value">{{cardInfo.wechat}}</view>
      </view>
      <view class="contact-item" bindtap="onCopyEmail" wx:if="{{cardInfo.email}}">
        <image class="contact-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/email.png" mode="aspectFit"></image>
        <view class="contact-value">{{cardInfo.email}}</view>
      </view>
      <view class="contact-item" bindtap="onViewAddress" wx:if="{{cardInfo.address}}">
        <image class="contact-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/address.png" mode="aspectFit"></image>
        <view class="contact-value">{{cardInfo.address}}</view>
      </view>
    </view>
  </view>
</view> 