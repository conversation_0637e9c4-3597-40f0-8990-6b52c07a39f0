/**
 * 名片模型
 */
module.exports = (sequelize, DataTypes) => {
  const BusinessCard = sequelize.define('BusinessCard', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '关联用户ID'
    },
    avatar: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '头像URL'
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '姓名'
    },
    company: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '公司'
    },
    position: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '职位'
    },
    industry: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '行业'
    },
    mobile: {
      type: DataTypes.STRING(20),
      allowNull: false,
      comment: '手机号'
    },
    wechat: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '微信号'
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '邮箱'
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '座机'
    },
    website: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '网址'
    },
    address: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '地址'
    },
    introduction: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '业务简介'
    },
    company_code: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '企业唯一标识码'
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否为默认名片，1是0否'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'business_cards',
    timestamps: true
  });

  return BusinessCard;
}; 