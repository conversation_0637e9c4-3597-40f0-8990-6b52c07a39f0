/**
 * 名片收藏模型
 */
module.exports = (sequelize, DataTypes) => {
  const CardCollection = sequelize.define('CardCollection', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '收藏者用户ID'
    },
    card_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '被收藏的名片ID'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'card_collections',
    timestamps: true,
    updatedAt: false
  });

  // 添加唯一约束防止重复收藏
  CardCollection.addHook('beforeValidate', (collection, options) => {
    return CardCollection.findOne({
      where: {
        user_id: collection.user_id,
        card_id: collection.card_id
      }
    }).then(existingCollection => {
      if (existingCollection && !collection.id) {
        throw new Error('该名片已被收藏');
      }
    });
  });

  return CardCollection;
}; 