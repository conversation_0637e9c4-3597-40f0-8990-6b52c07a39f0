const app = getApp();
const { get, post, put, delete: del } = require('./request');

/**
 * 获取当前用户名片信息
 * @returns {Promise} 名片信息promise
 */
const getMyCard = async () => {
  try {
    const cardInfo = await get('/card');
    // 更新全局名片信息
    app.globalData.cardInfo = cardInfo;
    return {
      code: 0,
      data: cardInfo
    };
  } catch (err) {
    console.error('获取名片信息失败', err);
    throw err;
  }
};

/**
 * 获取当前用户所有名片列表
 * @returns {Promise} 名片列表promise
 */
const getAllCards = async () => {
  try {
    const result = await get('/card/list');
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('获取名片列表失败', err);
    throw err;
  }
};

/**
 * 创建新名片
 * @param {Object} cardData - 名片数据
 * @returns {Promise} 新创建的名片信息promise
 */
const createNewCard = async (cardData) => {
  try {
    const cardInfo = await post('/card/create', cardData);
    return {
      code: 0,
      data: cardInfo
    };
  } catch (err) {
    console.error('创建新名片失败', err);
    throw err;
  }
};

/**
 * 创建或更新用户名片
 * @param {Object} cardData - 名片数据
 * @returns {Promise} 更新后的名片信息promise
 */
const saveCard = async (cardData) => {
  try {
    const cardInfo = await post('/card', cardData);
    // 更新全局名片信息
    app.globalData.cardInfo = cardInfo;
    
    // 更新企业编码
    if (cardInfo.companyCode) {
      app.globalData.userInfo.companyCode = cardInfo.companyCode;
      wx.setStorageSync('userInfo', app.globalData.userInfo);
    }
    
    return {
      code: 0,
      data: cardInfo
    };
  } catch (err) {
    console.error('保存名片信息失败', err);
    throw err;
  }
};

/**
 * 更新指定ID的名片
 * @param {number} id - 名片ID
 * @param {Object} cardData - 名片数据
 * @returns {Promise} 更新后的名片信息promise
 */
const updateCard = async (id, cardData) => {
  try {
    const cardInfo = await put(`/card/${id}`, cardData);
    
    // 如果更新的是当前默认名片，更新全局名片信息
    if (cardInfo.is_default) {
      app.globalData.cardInfo = cardInfo;
      
      // 更新企业编码
      if (cardInfo.companyCode) {
        app.globalData.userInfo.companyCode = cardInfo.companyCode;
        wx.setStorageSync('userInfo', app.globalData.userInfo);
      }
    }
    
    return {
      code: 0,
      data: cardInfo
    };
  } catch (err) {
    console.error('更新名片信息失败', err);
    throw err;
  }
};

/**
 * 设置默认名片
 * @param {number} id - 名片ID
 * @returns {Promise} 设置为默认的名片信息promise
 */
const setDefaultCard = async (id) => {
  try {
    const cardInfo = await put(`/card/${id}/default`);
    
    // 更新全局名片信息
    app.globalData.cardInfo = cardInfo;
    
    // 更新企业编码
    if (cardInfo.companyCode) {
      app.globalData.userInfo.companyCode = cardInfo.companyCode;
      wx.setStorageSync('userInfo', app.globalData.userInfo);
    }
    
    return {
      code: 0,
      data: cardInfo
    };
  } catch (err) {
    console.error('设置默认名片失败', err);
    throw err;
  }
};

/**
 * 删除名片
 * @param {number} id - 名片ID
 * @returns {Promise} 删除结果promise
 */
const deleteCard = async (id) => {
  try {
    const result = await del(`/card/${id}`);
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('删除名片失败', err);
    throw err;
  }
};

/**
 * 获取指定ID的名片信息
 * @param {number} id - 名片ID
 * @returns {Promise} 名片信息promise
 */
const getCardById = async (id) => {
  try {
    const result = await get(`/card/${id}`, {}, false);
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('获取名片信息失败', err);
    // 返回更具体的错误信息，而不是抛出异常
    return {
      code: err.code || 500,
      message: err.message || '获取名片信息失败'
    };
  }
};

/**
 * 收藏名片
 * @param {number} cardId - 名片ID
 * @returns {Promise} 收藏结果promise
 */
const collectCard = async (cardId) => {
  try {
    const result = await post('/collection', { cardId });
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('收藏名片失败', err);
    // 返回更具体的错误信息
    return {
      code: err.code || 500,
      message: err.message || '收藏名片失败'
    };
  }
};

/**
 * 获取名片夹列表
 * @param {number} [page=1] - 页码
 * @param {number} [pageSize=20] - 每页数量
 * @returns {Promise} 名片夹列表promise
 */
const getCardFolder = async (page = 1, pageSize = 20) => {
  try {
    const result = await get('/collection', { page, pageSize });
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('获取名片夹列表失败', err);
    throw err;
  }
};

/**
 * 删除收藏的名片
 * @param {number} collectionId - 收藏记录ID
 * @returns {Promise} 删除结果promise
 */
const removeCollection = async (collectionId) => {
  try {
    const result = await del(`/collection/${collectionId}`);
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('删除收藏名片失败', err);
    throw err;
  }
};

/**
 * 检查用户是否拥有名片
 * @returns {Promise} 用户是否有名片的promise
 */
const checkUserHasCard = async () => {
  // 检查是否已登录
  if (!app.globalData.userInfo || !wx.getStorageSync('token')) {
    return {
      code: 0,
      data: {
        isLoggedIn: false,
        hasCardInfo: false
      }
    };
  }
  
  try {
    // 尝试从后端API获取用户名片状态
    const result = await get('/card/check-status');
    
    // 如果请求成功并且有名片信息，更新全局名片数据
    if (result && result.cardInfo) {
      app.globalData.cardInfo = result.cardInfo;
      wx.setStorageSync('cardInfo', result.cardInfo);
    }
    
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('检查用户名片状态失败', err);
    
    // 尝试从本地获取名片状态作为备用
    if (app.globalData.cardInfo && app.globalData.cardInfo.id) {
      return {
        code: 0,
        data: {
          isLoggedIn: true,
          hasCardInfo: true,
          cardInfo: app.globalData.cardInfo
        }
      };
    }
    
    // 从本地存储获取
    const cardInfo = wx.getStorageSync('cardInfo');
    if (cardInfo && cardInfo.id) {
      app.globalData.cardInfo = cardInfo;
      return {
        code: 0,
        data: {
          isLoggedIn: true,
          hasCardInfo: true,
          cardInfo: cardInfo
        }
      };
    }
    
    // 如果无法确定，默认返回用户已登录但没有名片
    return {
      code: 0,
      data: {
        isLoggedIn: true,
        hasCardInfo: false
      }
    };
  }
};

module.exports = {
  getMyCard,
  getAllCards,
  createNewCard,
  saveCard,
  updateCard,
  setDefaultCard,
  deleteCard,
  getCardById,
  collectCard,
  getCardFolder,
  removeCollection,
  checkUserHasCard
}; 