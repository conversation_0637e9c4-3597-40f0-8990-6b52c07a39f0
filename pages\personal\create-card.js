const app = getApp();
const cardStore = require('../../store/card');

Page({
  data: {
    formData: {
      avatar: '',
      name: '',
      company: '',
      position: '',
      industry: '',
      mobile: '',
      wechat: '',
      email: '',
      phone: '',
      website: '',
      address: '',
      introduction: ''
    },
    industries: [
      '互联网/IT/电子/通信',
      '金融/投资/保险',
      '房地产/建筑',
      '商业服务/专业服务',
      '贸易/批发/零售/快消',
      '制造业',
      '医疗/健康/制药',
      '教育/培训',
      '文化/传媒/娱乐/体育',
      '能源/矿产/环保',
      '交通/物流/运输',
      '政府/非盈利组织',
      '农/林/牧/渔',
      '其他'
    ],
    avatarUrl: '',
    isSubmitting: false,
    showMoreOptions: false,
    isFirstCard: false // 是否是第一张名片
  },

  onLoad() {
    // 检查是否是第一张名片
    this.checkIsFirstCard();
    
    // 获取用户基本信息
    this.getUserBasicInfo();
  },
  
  // 检查是否是第一张名片
  checkIsFirstCard() {
    cardStore.getAllCards().then(res => {
      if (res.code === 0 && res.data) {
        const isFirstCard = !res.data.list || res.data.list.length === 0;
        this.setData({
          isFirstCard: isFirstCard
        });
      }
    }).catch(err => {
      console.error('获取名片列表失败', err);
      // 默认不是第一张名片
      this.setData({
        isFirstCard: false
      });
    });
  },

  // 获取用户基本信息
  getUserBasicInfo() {
    // 如果有默认名片，从中获取用户的基本联系信息
    if (app.globalData.cardInfo) {
      const cardInfo = app.globalData.cardInfo;
      this.setData({
        'formData.name': cardInfo.name || '',
        'formData.mobile': cardInfo.mobile || '',
        'formData.wechat': cardInfo.wechat || '',
        'formData.email': cardInfo.email || ''
        // 不预填公司和行业信息
      });
    } else {
      // 如果没有默认名片，尝试获取一下
      cardStore.getCardInfo().then(res => {
        if (res.code === 0 && res.data) {
          const cardInfo = res.data;
          this.setData({
            'formData.name': cardInfo.name || '',
            'formData.mobile': cardInfo.mobile || '',
            'formData.wechat': cardInfo.wechat || '',
            'formData.email': cardInfo.email || ''
            // 不预填公司和行业信息
          });
        }
      }).catch(err => {
        console.error('获取名片信息失败', err);
      });
    }
  },

  // 切换更多选项显示状态
  toggleMoreOptions() {
    this.setData({
      showMoreOptions: !this.data.showMoreOptions
    });
  },

  // 输入框事件处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 公司输入框变化处理
  onCompanyInput(e) {
    const value = e.detail.value;
    this.setData({
      'formData.company': value
    });
  },

  // 公司选择处理
  onCompanySelect(e) {
    const value = e.detail.value;
    const item = e.detail.item;

    console.log('选择了企业:', value, item);

    this.setData({
      'formData.company': value
    });

    // 可以在这里添加其他逻辑，比如自动填充其他相关信息
  },
  
  // 选择器变化处理
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: this.data.industries[value]
    });
  },

  // 选择头像
  chooseAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        // 上传头像
        wx.showLoading({
          title: '上传中...',
          mask: true
        });
        
        // 使用七牛云直传方式上传头像
        this.uploadAvatarToQiniu(tempFilePath);
      }
    });
  },

  // 获取微信头像（新版API）
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail;
    console.log('用户选择的微信头像URL:', avatarUrl);
    
    // 显示加载状态
    wx.showLoading({
      title: '处理头像中',
    });
    
    // 先显示临时图片
    this.setData({
      'formData.avatar': avatarUrl
    });
    
    // 上传到七牛云
    this.uploadAvatarToQiniu(avatarUrl);
  },

  // 七牛云上传头像
  uploadAvatarToQiniu(tempFilePath) {
    // 先获取上传凭证
    wx.request({
      url: `${app.globalData.baseUrl}/upload/token`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (tokenRes) => {
        if (tokenRes.data && tokenRes.data.code === 0 && tokenRes.data.data) {
          const uploadInfo = tokenRes.data.data;
          const userId = app.globalData.userInfo ? app.globalData.userInfo.id : 'user';
          const timestamp = Date.now();
          const ext = tempFilePath.substring(tempFilePath.lastIndexOf('.')) || '.jpg';
          const key = `${uploadInfo.pathPrefix}/${userId}_${timestamp}${ext}`;
          
          console.log('获取上传凭证成功，开始上传到七牛云');
          
          // 使用七牛云上传
          wx.uploadFile({
            url: uploadInfo.uploadUrl,
            filePath: tempFilePath,
            name: 'file',
            formData: {
              'token': uploadInfo.token,
              'key': key
            },
            timeout: 60000,
            success: (uploadRes) => {
              let data;
              try {
                data = JSON.parse(uploadRes.data);
                console.log('七牛云上传响应:', data);
              } catch (e) {
                console.error('解析上传响应失败:', e, uploadRes.data);
                wx.showToast({
                  title: '头像上传失败',
                  icon: 'none'
                });
                return;
              }
              
              if (data && data.key) {
                const fileUrl = `${uploadInfo.domain}/${key}`;
                console.log('上传成功，URL:', fileUrl);
                
                this.setData({
                  'formData.avatar': fileUrl
                });
                
                wx.showToast({
                  title: '头像设置成功',
                  icon: 'success'
                });
              } else {
                console.error('七牛云上传异常:', data);
                wx.showToast({
                  title: '头像上传失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              console.error('七牛云上传失败:', err);
              wx.showToast({
                title: '头像上传失败',
                icon: 'none'
              });
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        } else {
          console.error('获取上传凭证失败:', tokenRes);
          wx.showToast({
            title: '系统错误，请重试',
            icon: 'none'
          });
          wx.hideLoading();
        }
      },
      fail: (err) => {
        console.error('获取上传凭证请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        wx.hideLoading();
      }
    });
  },

  // 表单提交
  async submitForm() {
    // 表单验证
    const { name, company } = this.data.formData;
    
    if (!name) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!company) {
      wx.showToast({
        title: '请输入公司',
        icon: 'none'
      });
      return;
    }
    
    // 邮箱格式验证（如果有填写）
    const { email, mobile } = this.data.formData;
    if (email && !/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email)) {
      wx.showToast({
        title: '邮箱格式不正确',
        icon: 'none'
      });
      return;
    }
    
    // 手机号格式验证（如果有填写）
    if (mobile && !/^1\d{10}$/.test(mobile)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }
    
    // 提交表单
    this.setData({
      isSubmitting: true
    });
    
    wx.showLoading({
      title: '创建中...',
      mask: true
    });
    
    try {
      // 准备提交的数据
      const formDataWithCustomNames = { ...this.data.formData };
      
      // 设置自定义企业名称和产品中心名称
      const companyName = company.trim();
      const enterpriseName = `${companyName}AI名片`;
      const productsName = `${companyName}产品中心`;
      
      formDataWithCustomNames.enterpriseName = enterpriseName.slice(0, 30);
      formDataWithCustomNames.productsName = productsName.slice(0, 30);
      
      console.log('创建名片数据（含自定义名称）:', formDataWithCustomNames);
      
      const res = await cardStore.createNewCard(formDataWithCustomNames);
      
      if (res) {
        wx.hideLoading();
        wx.showToast({
          title: '创建成功',
          icon: 'success'
        });
        
        // 设置上一页需要刷新
        const pages = getCurrentPages();
        if (pages.length > 1) {
          const prevPage = pages[pages.length - 2];
          prevPage.setData({
            needRefresh: true
          });
        }
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1000);
      } else {
        wx.hideLoading();
        wx.showToast({
          title: '创建失败，请重试',
          icon: 'none'
        });
        this.setData({
          isSubmitting: false
        });
      }
    } catch (error) {
      console.error('创建名片失败', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '创建失败',
        icon: 'none'
      });
      this.setData({
        isSubmitting: false
      });
    }
  },

  // 取消创建
  cancelCreate() {
    wx.navigateBack();
  }
}); 