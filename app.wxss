/**app.wxss**/
/* 全局颜色变量 */
page {
  --primary-color: #3E7FFF; /* 主蓝色 */
  --primary-light: #6B9FFF; /* 浅蓝色 */
  --primary-dark: #2E5FBF; /* 深蓝色 */
  --primary-gradient: linear-gradient(to right, #3E7FFF, #4B8AFF); /* 蓝色渐变 */
  --secondary-color: #F8FAFF; /* 背景色 */
  --text-color: #333333; /* 主文字颜色 */
  --text-secondary: #666666; /* 次要文字颜色 */
  --text-light: #999999; /* 淡色文字 */
  --border-color: #EFEFEF; /* 边框颜色 */
  --success-color: #52C41A; /* 成功色 */
  --warning-color: #FAAD14; /* 警告色 */
  --error-color: #F5222D; /* 错误色 */
  
  font-size: 28rpx;
  color: var(--text-color);
  background-color: var(--secondary-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 通用布局容器 */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 卡片容器 */
.card {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  margin: 20rpx;
  padding: 30rpx;
  overflow: hidden;
}

/* 蓝色主按钮 */
.btn-primary {
  background: var(--primary-gradient);
  color: #FFFFFF;
  border: none;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 500;
  padding: 18rpx 0;
  width: 90%;
  margin: 30rpx auto;
  text-align: center;
  box-shadow: 0 8rpx 16rpx rgba(62, 127, 255, 0.2);
}

/* 辅助按钮 */
.btn-secondary {
  background-color: #FFFFFF;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 500;
  padding: 16rpx 0;
  width: 90%;
  margin: 30rpx auto;
  text-align: center;
}

/* 表单项样式 */
.form-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid var(--border-color);
}

.form-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.form-input {
  font-size: 30rpx;
  color: var(--text-color);
  width: 100%;
}

/* 通用间距 */
.padding {
  padding: 30rpx;
}

.margin {
  margin: 30rpx;
}

.margin-top {
  margin-top: 30rpx;
}

.margin-bottom {
  margin-bottom: 30rpx;
}

/* 文本样式 */
.text-primary {
  color: var(--primary-color);
}

.text-bold {
  font-weight: 600;
}

.text-center {
  text-align: center;
}

.text-small {
  font-size: 24rpx;
}

.text-large {
  font-size: 34rpx;
}

/* Flex布局工具 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}
