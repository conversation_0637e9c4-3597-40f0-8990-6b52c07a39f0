.container {
  width: 100%;
  height: 100vh;
  position: relative;
}

/* 加载中提示 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #F8FAFF;
  z-index: 10;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3E7FFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999999;
}

/* web-view 占满整个容器 */
web-view {
  width: 100%;
  height: 100%;
}

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  bottom: 80rpx;
  right: 0;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  z-index: 99999;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 12rpx 0 0 0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 1px solid rgba(230, 230, 230, 0.8);
  border-bottom: none;
}

/* 导航栏收起状态 */
.nav-bar.collapsed {
  width: auto;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.nav-bar.collapsed .nav-buttons {
  display: none;
}

.nav-bar.collapsed .toggle-btn {
  border: 1px solid rgba(230, 230, 230, 0.8);
  border-right: none;
  border-bottom: none;
}

/* 导航按钮区域 */
.nav-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6rpx 18rpx;
}

/* 单个导航按钮 */
.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 16rpx;
}

/* 按钮图标 */
.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 3rpx;
}

/* 按钮文字 */
.btn-text {
  font-size: 20rpx;
  color: #3E7FFF;
  text-align: center;
}

/* 切换按钮 */
.toggle-btn {
  padding: 9rpx 7rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx 0 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: -2rpx -2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(230, 230, 230, 0.8);
  border-right: none;
  border-bottom: none;
  z-index: 100000;
  min-width: 38rpx;
  position: relative;
}

/* 切换按钮文字 */
.toggle-text {
  font-size: 20rpx;
  color: #3E7FFF;
  text-align: center;
}

/* 垂直布局的toggle-text */
.toggle-text.vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 30rpx;
  padding: 5rpx 0;
  min-height: 90rpx;
}

/* 网格布局的toggle-text（可以保留，以后可能会用到） */
.toggle-text.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
  width: 60rpx;
  height: 60rpx;
  grid-gap: 4rpx;
}

/* 按钮中的字 */
.toggle-char {
  font-size: 18rpx;
  color: #3E7FFF;
  text-align: center;
  line-height: 1.1;
  padding: 1rpx 0;
  display: block;
}

/* 导航按钮区域中的分享按钮 */
.nav-btn-wrapper.share-btn {
  padding: 0;
  margin: 0 16rpx;
  background: none;
  border: none;
  outline: none;
  box-sizing: border-box;
  line-height: normal;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: auto;
  font-size: inherit;
  color: inherit;
  width: auto;
  font-weight: normal;
}

.nav-btn-wrapper.share-btn::after {
  border: none;
  outline: none;
  border-radius: 0;
}

.nav-btn-wrapper .nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
} 