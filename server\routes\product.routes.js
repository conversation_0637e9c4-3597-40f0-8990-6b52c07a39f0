/**
 * 产品路由
 */
const express = require('express');
const router = express.Router();
const productController = require('../controllers/product.controller');
const { verifyToken, optionalVerifyToken } = require('../middlewares/auth.middleware');

// 获取企业产品列表 - 添加中间件判断
// 当URL包含companyCode参数时，使用optionalVerifyToken允许未登录访问
router.get('/', (req, res, next) => {
  if (req.query.companyCode) {
    // 通过companyCode查询产品时不需要强制登录
    optionalVerifyToken(req, res, next);
  } else {
    // 查询当前用户关联的企业产品时需要登录验证
    verifyToken(req, res, next);
  }
}, productController.getProducts);

// 获取产品详情
router.get('/:id', optionalVerifyToken, productController.getProductById);

// 创建产品 (需要管理员权限)
router.post('/', verifyToken, productController.createProduct);

// 更新产品 (需要管理员权限)
router.put('/:id', verifyToken, productController.updateProduct);

// 删除产品 (需要管理员权限)
router.delete('/:id', verifyToken, productController.deleteProduct);

// 调整产品顺序 (需要管理员权限)
router.put('/order', verifyToken, productController.updateProductOrder);

module.exports = router; 