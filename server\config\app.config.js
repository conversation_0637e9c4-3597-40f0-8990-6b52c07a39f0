/**
 * 应用配置文件
 */
require('dotenv').config();

module.exports = {
  port: process.env.PORT || 3002,
  env: process.env.NODE_ENV || 'development',
  jwt: {
    secret: process.env.JWT_SECRET || 'zhilian_jwt_secret_key_for_weapp_2023',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },
  weapp: {
    appId: process.env.WX_APPID,
    appSecret: process.env.WX_SECRET
  },
  qiniu: {
    accessKey: process.env.QINIU_ACCESS_KEY,
    secretKey: process.env.QINIU_SECRET_KEY,
    bucket: process.env.QINIU_BUCKET,
    domain: process.env.QINIU_DOMAIN,
    pathPrefix: process.env.QINIU_PATH_PREFIX
  },
  upload: {
    dir: process.env.UPLOAD_DIR || 'uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || 5242880) // 5MB
  },
  apiPrefix: '/api/v1'
}; 