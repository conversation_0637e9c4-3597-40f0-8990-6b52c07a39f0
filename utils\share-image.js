/**
 * 名片分享图生成工具
 * 提供统一的接口用于生成名片分享图
 */

// 添加缓存对象
const shareImageCache = {};

/**
 * 生成名片分享图
 * @param {Object} cardInfo - 名片信息
 * @param {String} canvasId - Canvas元素ID
 * @param {Boolean} forceRefresh - 是否强制刷新
 * @returns {Promise<String>} 生成的图片临时路径
 */
const generateCardShareImage = (cardInfo, canvasId = 'shareCanvas', forceRefresh = false) => {
  return new Promise((resolve, reject) => {
    if (!cardInfo) {
      reject(new Error('缺少名片信息'));
      return;
    }
    
    // 生成缓存键
    const cacheKey = `card_${cardInfo.id}`;
    
    // 检查缓存
    if (!forceRefresh && shareImageCache[cacheKey] && shareImageCache[cacheKey].expireTime > Date.now()) {
      console.log('使用缓存的分享图:', shareImageCache[cacheKey].path);
      resolve(shareImageCache[cacheKey].path);
      return;
    }
    
    // 创建画布上下文
    const ctx = wx.createCanvasContext(canvasId);
    
    // 设置背景
    drawBackground(ctx);
    
    // 根据是否有头像，处理不同的绘制流程
    if (cardInfo.avatar) {
      // 有头像，先加载头像再绘制
      wx.getImageInfo({
        src: cardInfo.avatar,
        success: (res) => {
          console.log('头像加载成功:', res.path);
          // 绘制头像
          drawAvatar(ctx, res.path);
          
          // 绘制名片内容
          drawCardContent(ctx, cardInfo);
          
          // 完成绘制，转换为图片
          finishDrawing(ctx, canvasId)
            .then(tempFilePath => {
              // 缓存生成的图片，有效期30分钟
              shareImageCache[cacheKey] = {
                path: tempFilePath,
                expireTime: Date.now() + 30 * 60 * 1000
              };
              resolve(tempFilePath);
            })
            .catch(reject);
        },
        fail: (err) => {
          console.error('头像加载失败:', err);
          // 头像加载失败，使用默认头像
          drawDefaultAvatar(ctx);
          drawCardContent(ctx, cardInfo);
          
          finishDrawing(ctx, canvasId)
            .then(tempFilePath => {
              // 缓存生成的图片，有效期30分钟
              shareImageCache[cacheKey] = {
                path: tempFilePath,
                expireTime: Date.now() + 30 * 60 * 1000
              };
              resolve(tempFilePath);
            })
            .catch(reject);
        }
      });
    } else {
      // 没有头像，直接绘制默认头像和名片内容
      drawDefaultAvatar(ctx);
      drawCardContent(ctx, cardInfo);
      
      finishDrawing(ctx, canvasId)
        .then(tempFilePath => {
          // 缓存生成的图片，有效期30分钟
          shareImageCache[cacheKey] = {
            path: tempFilePath,
            expireTime: Date.now() + 30 * 60 * 1000
          };
          resolve(tempFilePath);
        })
        .catch(reject);
    }
  });
};

/**
 * 绘制背景
 * @param {Object} ctx - Canvas上下文
 */
const drawBackground = (ctx) => {
  // 高质感背景 - 使用天蓝色渐变
  const grd = ctx.createLinearGradient(0, 0, 300, 400);
  grd.addColorStop(0, '#1E88E5');  // 鲜亮的天蓝色
  grd.addColorStop(1, '#42A5F5');  // 更浅的天蓝色
  ctx.setFillStyle(grd);
  ctx.fillRect(0, 0, 300, 400);
  
  // 添加质感纹理 - 顶部光泽效果
  const grdTop = ctx.createLinearGradient(0, 0, 0, 150);
  grdTop.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
  grdTop.addColorStop(1, 'rgba(255, 255, 255, 0)');
  ctx.setFillStyle(grdTop);
  ctx.fillRect(0, 0, 300, 150);
  
  // 添加质感元素 - 右上角装饰
  ctx.setFillStyle('rgba(255, 255, 255, 0.18)');
  ctx.beginPath();
  ctx.moveTo(300, 0);
  ctx.lineTo(300, 120);
  ctx.lineTo(180, 0);
  ctx.closePath();
  ctx.fill();
  
  // 左下角装饰元素
  ctx.setFillStyle('rgba(255, 255, 255, 0.15)');
  ctx.beginPath();
  ctx.moveTo(0, 400);
  ctx.lineTo(120, 400);
  ctx.lineTo(0, 280);
  ctx.closePath();
  ctx.fill();
  
  // 添加细微的点状纹理
  ctx.setFillStyle('rgba(255, 255, 255, 0.1)');
  for (let i = 0; i < 30; i++) {
    for (let j = 0; j < 40; j++) {
      if ((i + j) % 3 === 0) {
        ctx.beginPath();
        ctx.arc(i * 10, j * 10, 1, 0, 2 * Math.PI);
        ctx.fill();
      }
    }
  }
  
  // 添加专业感的横线
  ctx.setStrokeStyle('rgba(255, 255, 255, 0.15)');
  ctx.setLineWidth(0.5);
  for (let i = 1; i < 10; i++) {
    ctx.beginPath();
    ctx.moveTo(0, i * 40);
    ctx.lineTo(300, i * 40);
    ctx.stroke();
  }
};

/**
 * 绘制头像
 * @param {Object} ctx - Canvas上下文
 * @param {String} avatarPath - 头像图片路径
 */
const drawAvatar = (ctx, avatarPath) => {
  // 由于已经有了头像图片路径，我们可以直接绘制，但需要保持比例
  ctx.save();
  ctx.beginPath();
  ctx.arc(250, 60, 40, 0, 2 * Math.PI);
  ctx.clip();
  
  // 先绘制白色背景
  ctx.fillStyle = '#FFFFFF';
  ctx.fillRect(210, 20, 80, 80);
  
  // 绘制头像 - 使用更智能的方式绘制以避免拉伸
  // 注意：这里无需获取图片信息，因为传入的avatarPath已经是临时文件路径
  // 使用二次绘制法：先绘制到中间画布，再绘制到目标位置
  try {
    // 正方形区域绘制，保持比例并居中
    const size = 80; // 头像大小
    const x = 210;   // 左上角X坐标
    const y = 20;    // 左上角Y坐标
    
    // 绘制头像到正方形区域
    // 注意：微信小程序的ctx.drawImage可以根据图片原始比例自动处理
    ctx.drawImage(avatarPath, x, y, size, size);
    
    // 添加边框
    ctx.setStrokeStyle('rgba(255, 255, 255, 0.3)');
    ctx.setLineWidth(2);
    ctx.stroke();
  } catch (error) {
    console.error('绘制头像失败，使用简单绘制方式:', error);
    // 简单绘制作为后备方案
    ctx.drawImage(avatarPath, 210, 20, 80, 80);
    
    ctx.setStrokeStyle('rgba(255, 255, 255, 0.3)');
    ctx.setLineWidth(2);
    ctx.stroke();
  }
  
  ctx.restore();
};

/**
 * 绘制默认头像
 * @param {Object} ctx - Canvas上下文
 */
const drawDefaultAvatar = (ctx) => {
  ctx.save();
  ctx.beginPath();
  ctx.arc(250, 60, 40, 0, 2 * Math.PI);
  ctx.clip();
  
  // 填充白色背景
  ctx.fillStyle = '#FFFFFF';
  ctx.fillRect(210, 20, 80, 80);
  
  // 渐变背景默认头像
  const avatarGrd = ctx.createLinearGradient(210, 20, 290, 100);
  avatarGrd.addColorStop(0, '#7F8C8D');
  avatarGrd.addColorStop(1, '#BDC3C7');
  ctx.setFillStyle(avatarGrd);
  ctx.fillRect(210, 20, 80, 80);
  
  // 添加边框
  ctx.setStrokeStyle('rgba(255, 255, 255, 0.3)');
  ctx.setLineWidth(2);
  ctx.stroke();
  
  ctx.restore();
};

/**
 * 绘制名片内容
 * @param {Object} ctx - Canvas上下文
 * @param {Object} cardInfo - 名片信息
 */
const drawCardContent = (ctx, cardInfo) => {
  // 绘制名称
  ctx.setShadow(1, 1, 3, 'rgba(0, 0, 0, 0.2)');
  ctx.setFillStyle('#FFFFFF');
  ctx.setFontSize(22);
  ctx.setTextBaseline('top');
  ctx.fillText(cardInfo.name || '未填写姓名', 30, 40);
  ctx.setShadow(0, 0, 0, 'rgba(0, 0, 0, 0)');
  
  // 绘制公司
  ctx.setFontSize(14);
  ctx.setFillStyle('rgba(255, 255, 255, 0.95)');
  ctx.fillText(cardInfo.company || '未填写公司', 30, 70);
  
  // 绘制职位
  if (cardInfo.position) {
    ctx.setFontSize(12);
    ctx.setFillStyle('rgba(255, 255, 255, 0.85)');
    ctx.fillText(cardInfo.position, 30, 94);
  }
  
  // 绘制分隔线 - 更精致的渐变设计
  const lineGrd = ctx.createLinearGradient(30, 130, 270, 130);
  lineGrd.addColorStop(0, 'rgba(255, 255, 255, 0.05)');
  lineGrd.addColorStop(0.5, 'rgba(255, 255, 255, 0.3)');
  lineGrd.addColorStop(1, 'rgba(255, 255, 255, 0.05)');
  ctx.setStrokeStyle(lineGrd);
  ctx.setLineWidth(1);
  ctx.beginPath();
  ctx.moveTo(30, 130);
  ctx.lineTo(270, 130);
  ctx.stroke();
  
  // 绘制手机号
  let yPosition = 140;
  if (cardInfo.mobile) {
    drawContactItem(ctx, cardInfo.mobile, yPosition, 'phone');
    yPosition += 28;
  }
  
  // 绘制邮箱
  if (cardInfo.email) {
    const email = cardInfo.email.length > 25 ? cardInfo.email.substring(0, 22) + '...' : cardInfo.email;
    drawContactItem(ctx, email, yPosition, 'email');
    yPosition += 28;
  }
  
  // 绘制地址
  if (cardInfo.address) {
    const address = cardInfo.address.length > 25 ? cardInfo.address.substring(0, 22) + '...' : cardInfo.address;
    drawContactItem(ctx, address, yPosition, 'address');
  }
  
  // 绘制底部装饰线
  ctx.setStrokeStyle('rgba(255, 255, 255, 0.15)');
  ctx.setLineWidth(1);
  ctx.beginPath();
  ctx.moveTo(30, 350);
  ctx.lineTo(270, 350);
  ctx.stroke();
  
  // 绘制底部小程序名称
  ctx.setFillStyle('rgba(255, 255, 255, 0.7)');
  ctx.setFontSize(12);
  ctx.fillText('智链名片', 30, 370);
};

/**
 * 绘制联系方式
 * @param {Object} ctx - Canvas上下文
 * @param {String} text - 联系方式文本
 * @param {Number} yPosition - Y轴位置
 * @param {String} type - 类型 (phone, email, address)
 */
const drawContactItem = (ctx, text, yPosition, type) => {
  // 绘制文本
  ctx.setFillStyle('#FFFFFF');
  ctx.setFontSize(14);
  ctx.fillText(text, 60, yPosition);
  
  // 绘制图标
  drawContactIcon(ctx, yPosition, type);
};

/**
 * 绘制联系方式图标
 * @param {Object} ctx - Canvas上下文
 * @param {Number} yPosition - Y轴位置
 * @param {String} type - 类型 (phone, email, address)
 */
const drawContactIcon = (ctx, yPosition, type) => {
  // 定义图标链接映射
  const iconMap = {
    'phone': 'https://pic.sdtaa.com/ZhiLian/Picture/Project/common/phone-white.png',
    'email': 'https://pic.sdtaa.com/ZhiLian/Picture/Project/common/email-white.png',
    'address': 'https://pic.sdtaa.com/ZhiLian/Picture/Project/common/address-white.png'
  };
  
  // 优先使用云存储图标
  if (iconMap[type]) {
    wx.getImageInfo({
      src: iconMap[type],
      success: (res) => {
        // 绘制图标，调整大小为20x20像素，并调整位置使其居中对齐
        ctx.drawImage(res.path, 28, yPosition - 2, 20, 20);
        ctx.draw(true);
      },
      fail: (err) => {
        console.error('加载图标失败:', err);
        // 加载失败时使用默认绘制方式
        drawFallbackIcon(ctx, yPosition, type);
      }
    });
  } else {
    // 没有映射时使用默认绘制方式
    drawFallbackIcon(ctx, yPosition, type);
  }
};

/**
 * 当图标加载失败时使用简单绘制（备用方案）
 * @param {Object} ctx - Canvas上下文
 * @param {Number} yPosition - Y轴位置
 * @param {String} type - 类型 (phone, email, address)
 */
const drawFallbackIcon = (ctx, yPosition, type) => {
  // 绘制高质感图标背景 - 半透明圆形
  ctx.beginPath();
  ctx.arc(38, yPosition + 8, 10, 0, 2 * Math.PI);
  ctx.setFillStyle('rgba(255, 255, 255, 0.15)');
  ctx.fill();
  
  ctx.setStrokeStyle('#FFFFFF');
  ctx.setLineWidth(1.5);
  
  if (type === 'phone') {
    // 电话图标
    ctx.beginPath();
    ctx.arc(38, yPosition + 8, 5, 0, 2 * Math.PI);
    ctx.moveTo(38, yPosition + 8);
    ctx.lineTo(38, yPosition + 3);
    ctx.stroke();
  } else if (type === 'email') {
    // 邮件图标
    ctx.beginPath();
    ctx.rect(33, yPosition + 5, 10, 6);
    ctx.moveTo(33, yPosition + 5);
    ctx.lineTo(38, yPosition + 8);
    ctx.lineTo(43, yPosition + 5);
    ctx.stroke();
  } else if (type === 'address') {
    // 地址图标
    ctx.beginPath();
    ctx.arc(38, yPosition + 6, 3, 0, 2 * Math.PI);
    ctx.moveTo(38, yPosition + 6);
    ctx.lineTo(38, yPosition + 11);
    ctx.stroke();
  }
};

/**
 * 完成绘制并转换为图片
 * @param {Object} ctx - Canvas上下文
 * @param {String} canvasId - Canvas元素ID
 * @returns {Promise<String>} 生成的图片临时路径
 */
const finishDrawing = (ctx, canvasId) => {
  return new Promise((resolve, reject) => {
    // 增加绘制完成的回调
    ctx.draw(true, () => {
      // 绘制结束后延迟一定时间再转换为图片
      setTimeout(() => {
        wx.canvasToTempFilePath({
          canvasId: canvasId,
          fileType: 'jpg',
          quality: 0.9,
          success: (res) => {
            console.log('生成分享图片成功:', res.tempFilePath);
            resolve(res.tempFilePath);
          },
          fail: (err) => {
            console.error('生成分享图片失败:', err);
            // 尝试一次重试，增加延迟时间
            setTimeout(() => {
              wx.canvasToTempFilePath({
                canvasId: canvasId,
                fileType: 'jpg',
                quality: 0.8, // 降低质量再试一次
                success: (res) => {
                  console.log('重试生成分享图片成功:', res.tempFilePath);
                  resolve(res.tempFilePath);
                },
                fail: (retryErr) => {
                  console.error('重试生成分享图片失败:', retryErr);
                  // 最后的降级处理，返回默认图片
                  console.log('使用默认分享图');
                  resolve('https://pic.sdtaa.com/ZhiLian/Picture/Project/default-share.png');
                }
              });
            }, 800); // 增加延迟时间
          }
        });
      }, 800); // 增加延迟时间，确保Canvas已渲染完成
    });
  });
};

/**
 * 确保Canvas元素存在于页面中
 * @param {Object} page - 页面实例
 * @param {String} selector - Canvas选择器
 * @returns {Promise<Boolean>} 是否存在
 */
const ensureCanvasExists = (page, selector = '#shareCanvas') => {
  return new Promise((resolve) => {
    const query = wx.createSelectorQuery().in(page);
    query.select(selector).fields({ node: true, size: true }).exec((res) => {
      const canvas = res[0];
      resolve(!!canvas);
    });
  });
};

/**
 * 清除分享图缓存
 * @param {String} cardId - 名片ID，不传则清除所有缓存
 */
const clearShareImageCache = (cardId) => {
  if (cardId) {
    delete shareImageCache[`card_${cardId}`];
  } else {
    Object.keys(shareImageCache).forEach(key => {
      delete shareImageCache[key];
    });
  }
};

/**
 * 检查是否有缓存的分享图
 * @param {String} cardId - 名片ID
 * @returns {String|null} 缓存的分享图路径，没有则返回null
 */
const getShareImageFromCache = (cardId) => {
  const cacheKey = `card_${cardId}`;
  if (shareImageCache[cacheKey] && shareImageCache[cacheKey].expireTime > Date.now()) {
    return shareImageCache[cacheKey].path;
  }
  return null;
};

module.exports = {
  generateCardShareImage,
  ensureCanvasExists,
  clearShareImageCache,
  getShareImageFromCache
}; 