.container {
  padding: 30rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
  box-sizing: border-box;
}

.header {
  margin-bottom: 40rpx;
  padding: 10rpx 0;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3E7FFF;
  margin: 0 10rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 未关联企业提示 */
.no-company {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.no-company-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.no-company-text {
  font-size: 28rpx;
  color: #666;
}

/* 表单样式 */
.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  transition: box-shadow 0.3s;
}

.form-container:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.form-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
  line-height: 1.6;
}

.form-subtitle::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background-color: #3E7FFF;
  border-radius: 50%;
}

.form-item {
  position: relative;
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.form-input {
  height: 80rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
}

.char-count {
  position: absolute;
  right: 10rpx;
  bottom: -5rpx;
  font-size: 24rpx;
  color: #999;
}

/* 提示信息 */
.tips {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 25rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  position: relative;
  padding-left: 20rpx;
  line-height: 1.6;
}

.tip-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background-color: #3E7FFF;
  border-radius: 50%;
}

/* 分享图预览模块 */
.share-images-container {
  padding-bottom: 25rpx;
}

.share-images-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 25rpx;
  padding: 5rpx 0;
}

.share-image-item {
  width: 48%;
  transition: transform 0.2s;
}

.share-image-item:hover {
  transform: translateY(-2rpx);
}

.share-image-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 14rpx;
  text-align: center;
  font-weight: 500;
}

.share-image-wrapper {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  background-color: #f5f7fa;
  transition: transform 0.2s, box-shadow 0.2s;
}

.share-image-wrapper:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.15);
}

.share-image {
  width: 100%;
  height: 100%;
}

.share-image-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;
}

.share-image-wrapper:active .share-image-mask {
  opacity: 1;
}

.preview-text {
  color: #ffffff;
  font-size: 28rpx;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  font-weight: 500;
  letter-spacing: 1rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-images-note {
  margin-bottom: 0;
  padding: 5rpx 0;
}

/* 保存按钮 */
.save-button {
  background: linear-gradient(135deg, #4e8cff 0%, #3E7FFF 100%);
  color: #fff;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  letter-spacing: 2rpx;
  box-shadow: 0 6rpx 15rpx rgba(62, 127, 255, 0.25);
  transition: all 0.3s;
}

.save-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 8rpx rgba(62, 127, 255, 0.2);
}

.save-button[disabled] {
  background: linear-gradient(135deg, #a0bfff 0%, #8fb2ff 100%) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 0 4rpx 10rpx rgba(62, 127, 255, 0.15);
} 