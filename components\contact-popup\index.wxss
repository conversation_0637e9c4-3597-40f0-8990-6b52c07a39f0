.contact-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.contact-popup-mask.show {
  opacity: 1;
  visibility: visible;
}

.contact-popup-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.contact-popup-mask.show .contact-popup-content {
  transform: scale(1);
}

.contact-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-popup-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #3E7FFF, #6B9FFF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.contact-popup-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
}

.contact-popup-body {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-text {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.service-item {
  margin-top: 10rpx;
  color: #555;
}

.contact-hint {
  margin-top: 20rpx;
  color: #3E7FFF;
  font-weight: 500;
}

.contact-qrcode {
  width: 300rpx;
  height: 300rpx;
  margin: 20rpx 0;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
} 