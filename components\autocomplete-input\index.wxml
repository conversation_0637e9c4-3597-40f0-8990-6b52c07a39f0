<!-- 遮罩层，用于捕获外部点击 -->
<view class="mask" wx:if="{{showDropdown}}" bindtap="onClickOutside"></view>

<view class="autocomplete-container">
  <!-- 标签 -->
  <view class="form-label" wx:if="{{label}}">
    {{label}}
    <text class="required" wx:if="{{required}}">*</text>
  </view>

  <!-- 输入框容器 -->
  <view class="input-container">
    <input
      class="form-input"
      value="{{value}}"
      placeholder="{{placeholder}}"
      bindinput="onInput"
      bindfocus="onFocus"
      bindblur="onBlur"
    />

    <!-- 清空按钮 -->
    <view class="clear-btn" wx:if="{{value && isFocused}}" bindtap="onClear">
      <view class="clear-icon">×</view>
    </view>

    <!-- 搜索加载指示器 -->
    <view class="loading-indicator" wx:if="{{isSearching}}">
      <view class="loading-icon"></view>
    </view>
  </view>

  <!-- 下拉建议列表 -->
  <view class="dropdown" wx:if="{{showDropdown && suggestions.length > 0}}" catchtap="stopPropagation">
    <scroll-view class="dropdown-scroll" scroll-y="true" style="max-height: 400rpx;">
      <view
        class="dropdown-item"
        wx:for="{{suggestions}}"
        wx:key="index"
        data-index="{{index}}"
        bindtap="onSelectItem"
      >
        <text class="dropdown-text">{{item[displayField]}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 无结果提示 -->
  <view class="no-results" wx:if="{{showDropdown && suggestions.length === 0 && !isSearching && value.length >= minSearchLength}}" catchtap="stopPropagation">
    <text class="no-results-text">未找到匹配的企业</text>
  </view>
</view>
