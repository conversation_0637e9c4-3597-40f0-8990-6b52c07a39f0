.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f8faff;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding-bottom: 100rpx; /* 底部安全区域 */
}

/* 加载状态 */
.loading-container {
  width: 100%;
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.loading-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3E7FFF;
  animation: bounce 1.4s infinite ease-in-out both;
  opacity: 0.6;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* WebView样式 - 全屏显示 */
.full-webview {
  width: 100%;
  height: 100%;
  flex: 1;
}

/* 默认页面样式 */
.default-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  height: 80vh;
  width: 100%;
  box-sizing: border-box;
}

.default-image {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 40rpx;
}

.default-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  text-align: center;
}

.default-subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.action-btn {
  background-color: #3E7FFF;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 60rpx;
  border-radius: 40rpx;
  border: none;
  margin-top: 30rpx;
}

/* 二维码样式 */
.contact-qrcode {
  width: 300rpx;
  height: 300rpx;
  margin: 30rpx 0;
}

.qrcode-tip {
  font-size: 26rpx;
  color: #999;
  text-align: center;
}

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  bottom: 80rpx;
  right: 0;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  z-index: 99999;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 12rpx 0 0 12rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 1px solid rgba(230, 230, 230, 0.8);
}

/* 导航栏收起状态 */
.nav-bar.collapsed {
  width: auto;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.nav-bar.collapsed .nav-buttons {
  display: none;
}

.nav-bar.collapsed .toggle-btn {
  border: 1px solid rgba(230, 230, 230, 0.8);
  border-right: none;
}

/* 导航按钮区域 */
.nav-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6rpx 18rpx;
}

/* 单个导航按钮 */
.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 16rpx;
}

/* 按钮图标 */
.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 3rpx;
}

/* 按钮文字 */
.btn-text {
  font-size: 20rpx;
  color: #3E7FFF;
  text-align: center;
}

/* 切换按钮 */
.toggle-btn {
  padding: 9rpx 7rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx 0 0 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: -4rpx 0 10rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(230, 230, 230, 0.8);
  border-right: none;
  z-index: 100000;
  min-width: 38rpx;
  position: relative;
}

/* 切换按钮文字 */
.toggle-text {
  font-size: 20rpx;
  color: #3E7FFF;
  text-align: center;
}

/* 垂直布局的toggle-text */
.toggle-text.vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 30rpx;
  padding: 5rpx 0;
  min-height: 90rpx;
}

/* 按钮中的字 */
.toggle-char {
  font-size: 18rpx;
  color: #3E7FFF;
  text-align: center;
  line-height: 1.1;
  padding: 1rpx 0;
  display: block;
}

/* 导航按钮区域中的分享按钮 */
.nav-btn-wrapper.share-btn {
  padding: 0;
  margin: 0 16rpx;
  background: none;
  border: none;
  outline: none;
  box-sizing: border-box;
  line-height: normal;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: auto;
  font-size: inherit;
  color: inherit;
  width: auto;
}

.nav-btn-wrapper.share-btn::after {
  border: none;
  outline: none;
  border-radius: 0;
}

.nav-btn-wrapper .nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}