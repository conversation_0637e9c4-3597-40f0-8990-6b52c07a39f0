.textarea-container {
  margin-bottom: 28rpx;
}

.textarea-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 18rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.required {
  color: #ff4d4f;
  margin-right: 6rpx;
  font-size: 32rpx;
  line-height: 1;
}

.textarea-wrapper {
  position: relative;
  background-color: rgba(245, 247, 250, 0.8);
  border: 2rpx solid rgba(62, 127, 255, 0.1);
  border-radius: 12rpx;
  padding: 24rpx;
  box-sizing: border-box;
  min-height: 200rpx;
  transition: all 0.3s;
}

.textarea-wrapper:focus-within {
  border-color: rgba(62, 127, 255, 0.5);
  background-color: #ffffff;
  box-shadow: 0 4rpx 8rpx rgba(62, 127, 255, 0.1);
}

.textarea-control {
  width: 100%;
  min-height: 150rpx;
  font-size: 30rpx;
  color: #333333;
  line-height: 1.6;
}

.textarea-control::placeholder {
  color: #999999;
  font-size: 28rpx;
}

.textarea-count {
  position: absolute;
  right: 20rpx;
  bottom: 14rpx;
  font-size: 24rpx;
  color: #999999;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
} 