<view class="textarea-container">
  <view class="textarea-label">
    <text class="required" wx:if="{{required}}">*</text>
    <text>{{label}}</text>
  </view>
  <view class="textarea-wrapper" style="height: {{height}}rpx;">
    <textarea 
      class="textarea-control" 
      value="{{value}}" 
      placeholder="{{placeholder}}" 
      maxlength="{{maxlength}}" 
      disabled="{{disabled}}" 
      focus="{{focus}}" 
      bindinput="onInput" 
      bindfocus="onFocus" 
      bindblur="onBlur"
      style="height: {{height - 40}}rpx;"
    ></textarea>
    <view class="textarea-count" wx:if="{{showCount}}">
      <text>{{currentLength}}/{{maxlength}}</text>
    </view>
  </view>
</view> 