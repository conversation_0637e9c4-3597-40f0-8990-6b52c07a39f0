Component({
  /**
   * 组件的属性列表
   */
  properties: {
    label: {
      type: String,
      value: ''
    },
    placeholder: {
      type: String,
      value: '请输入'
    },
    value: {
      type: String,
      value: ''
    },
    name: {
      type: String,
      value: ''
    },
    required: {
      type: <PERSON>olean,
      value: false
    },
    maxlength: {
      type: Number,
      value: 500
    },
    height: {
      type: Number,
      value: 200
    },
    disabled: {
      type: Boolean,
      value: false
    },
    focus: {
      type: Boolean,
      value: false
    },
    showCount: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentLength: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 输入事件
    onInput(e) {
      const { value } = e.detail;
      this.setData({
        currentLength: value.length
      });
      this.triggerEvent('input', { name: this.properties.name, value });
    },
    
    // 聚焦事件
    onFocus() {
      this.triggerEvent('focus', { name: this.properties.name });
    },
    
    // 失焦事件
    onBlur() {
      this.triggerEvent('blur', { name: this.properties.name, value: this.properties.value });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.setData({
        currentLength: this.properties.value ? this.properties.value.length : 0
      });
    }
  }
}) 