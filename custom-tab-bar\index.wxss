.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 116rpx;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 999;
}

.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  padding: 10rpx 0;
}

.tab-bar-item cover-image {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 8rpx;
  flex-shrink: 0;
}

.tab-bar-item cover-view {
  font-size: 24rpx;
  line-height: 1.3;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
} 