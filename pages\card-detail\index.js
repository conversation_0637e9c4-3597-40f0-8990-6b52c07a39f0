const app = getApp()

// 确保全局缓存对象存在
if (!wx.cardDetailCache) {
  wx.cardDetailCache = {
    enterprise: {},
    products: {},
    personal: {}
  };
}

Page({
  data: {
    cardId: null,
    cardInfo: null,
    isLoading: true,
    isCollected: false,
    isOwner: false,
    userInfo: null,
    isCollecting: false,
    companyInfo: null,
    selected: 'personal',
    showEnterpriseWebView: false,
    showProductsWebView: false,
    enterpriseUrl: '',
    productsUrl: '',
    shareImagePath: '', // 添加分享图路径存储
    isGeneratingShareImage: false, // 分享图生成中的状态标志
    hasMyCard: false, // 是否已有个人名片
    pendingTargetTab: null,
    // 添加自定义名称字段
    enterpriseName: '企业',
    productsName: '产品中心',
    fromShared: false, // 是否是从名片夹进入的分享名片
    hasProducts: false // 是否有产品
  },

  onLoad(options) {
    console.log("名片详情页onLoad, 选项:", options);

    // 检查是否是从分享进入的名片
    if (options.fromShared === 'true' || options.fromShare === 'true') {
      console.log("从分享进入的名片");
      this.setData({
        fromShared: true
      });
    }

    // 确保TabBar在名片详情页显示
    this.ensureTabBarVisibility();

    // 获取目标标签页参数，默认为"personal"
    const targetTab = options.targetTab || 'personal';
    
    // 设置选中标签
    this.setData({
      selected: targetTab
    });
    
    // 保存当前选中的标签页到本地存储
    wx.setStorageSync('card_detail_selected_tab', targetTab);
    
    // 使用后端API检查用户是否已登录且有名片
    this.checkUserHasCard();
    
    // 尝试从本地存储加载全局用户信息（确保globalData中有数据）
    if (!app.globalData.userInfo) {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        app.globalData.userInfo = userInfo;
        console.log("从本地存储加载用户信息:", userInfo);
      }
    }
    
    // 尝试从本地存储加载全局名片信息（确保globalData中有数据）
    if (!app.globalData.cardInfo) {
      const cardInfo = wx.getStorageSync('cardInfo');
      if (cardInfo) {
        app.globalData.cardInfo = cardInfo;
        console.log("从本地存储加载名片信息:", cardInfo);
        
        // 更新名片状态标志
        this.setData({
          hasMyCard: true
        });
      }
    }
    
    if (options.id) {
      const cardId = options.id;
      this.setData({
        cardId: cardId
      })
      console.log("设置cardId:", cardId);
      
      // 如果用户已登录，无论有没有缓存都强制重新检查收藏状态
      if (app.globalData.userInfo && app.globalData.userInfo.userId) {
        // 强制重新检查收藏状态，不使用缓存
        if (wx.cardDetailCache.personal[cardId]) {
          delete wx.cardDetailCache.personal[cardId];
        }
      }
      
      // 检查是否有缓存数据，但不使用收藏状态缓存
      if (wx.cardDetailCache.personal[cardId]) {
        console.log("使用缓存的个人页面数据");
        const cachedData = wx.cardDetailCache.personal[cardId];
        this.setData({
          cardInfo: cachedData.cardInfo,
          isOwner: cachedData.isOwner,
          companyInfo: cachedData.companyInfo,
          isLoading: false,
          // 如果有企业信息，设置自定义名称
          enterpriseName: cachedData.companyInfo && cachedData.companyInfo.enterpriseName ? cachedData.companyInfo.enterpriseName : '企业',
          productsName: cachedData.companyInfo && cachedData.companyInfo.productsName ? cachedData.companyInfo.productsName : '产品中心'
        });
        
        // 始终重新检查收藏状态，确保最新
        if (app.globalData.userInfo && app.globalData.userInfo.userId) {
          this.checkCollectionStatus(cardId);
        }
        
        // 如果卡片信息已加载，尝试生成分享图
        if (cachedData.cardInfo) {
          this.generateShareImage(cachedData.cardInfo);
        }
        
        // 根据targetTab参数自动切换到对应标签页
        setTimeout(() => {
          this.switchToTargetTab(targetTab);
        }, 300);
      } else {
        this.loadCardDetail().then(() => {
          // 数据加载完成后，根据targetTab参数自动切换到对应标签页
          this.switchToTargetTab(targetTab);
        });
      }
    } else {
      console.log("未找到名片ID");
      wx.showToast({
        title: '未找到名片信息',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
    
    // 获取用户信息
    if (app.globalData.userInfo) {
      console.log("获取到全局用户信息:", app.globalData.userInfo);
      this.setData({
        userInfo: app.globalData.userInfo
      })
    } else {
      console.log("未找到全局用户信息");
    }
  },

  // 页面显示时设置底部标签选中状态
  onShow() {
    // 确保TabBar在名片详情页显示
    this.ensureTabBarVisibility();
    
    // 如果有缓存的选中状态，则恢复
    const selectedTab = wx.getStorageSync('card_detail_selected_tab') || 'personal'
    this.setData({
      selected: selectedTab
    })
    
    // 尝试从本地存储加载全局用户信息
    if (!app.globalData.userInfo) {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        app.globalData.userInfo = userInfo;
        console.log("onShow - 从本地存储加载用户信息:", userInfo);
      }
    }
    
    // 尝试从本地存储加载全局名片信息
    if (!app.globalData.cardInfo) {
      const cardInfo = wx.getStorageSync('cardInfo');
      if (cardInfo) {
        app.globalData.cardInfo = cardInfo;
        console.log("onShow - 从本地存储加载名片信息:", cardInfo);
      }
    }
    
    // 使用后端API检查用户是否有名片，确保状态最新
    this.checkUserHasCard();
    
    // 如果用户已登录并且有名片ID，每次页面显示时重新检查收藏状态
    if (app.globalData.userInfo && app.globalData.userInfo.userId && this.data.cardId) {
      // 清除当前缓存的收藏状态，强制重新检查
      if (wx.cardDetailCache.personal[this.data.cardId]) {
        delete wx.cardDetailCache.personal[this.data.cardId];
      }
      
      // 重新检查收藏状态
      this.checkCollectionStatus(this.data.cardId);
    }
    
    // 检查是否需要重新生成分享图（例如从企业页面返回）
    if (this.data.cardInfo && (!this.data.shareImagePath || this.data.shareImagePath === '')) {
      console.log("页面重新显示，需要重新生成分享图");
      this.generateShareImage(this.data.cardInfo);
    }
    
    // 如果之前分享图生成失败，尝试重新生成
    if (this.data.cardInfo && this.data.isGeneratingShareImage === false && !this.data.shareImagePath) {
      console.log("之前分享图生成失败，尝试重新生成");
      // 清除分享图缓存
      const shareImage = require('../../utils/share-image.js');
      if (this.data.cardInfo.id) {
        shareImage.clearShareImageCache(this.data.cardInfo.id);
      }
      // 重新生成分享图
      this.generateShareImage(this.data.cardInfo);
    }
  },

  // 确保TabBar在名片详情页正确显示
  ensureTabBarVisibility() {
    const app = getApp();
    
    // 使用全局控制对象控制TabBar
    if (app.globalData.tabBarControl) {
      // 名片详情页应该显示TabBar
      if (!app.globalData.tabBarControl.show) {
        app.globalData.tabBarControl.show = true;
        
        // 获取当前页面的TabBar
        if (typeof this.getTabBar === 'function') {
          const tabBar = this.getTabBar();
          if (tabBar && typeof tabBar.setTabBarShow === 'function') {
            tabBar.setTabBarShow(true, false); // 显示TabBar且暂停自动检查
          }
        }
      }
    }
  },

  // 加载名片详情
  loadCardDetail() {
    console.log("开始加载名片详情, cardId:", this.data.cardId);
    
    wx.showLoading({
      title: '加载中',
    })
    
    this.setData({
      isLoading: true
    })
    
    const card = require('../../utils/card.js')
    return card.getCardById(this.data.cardId).then(res => {
      console.log("获取名片详情返回:", res);
      
      if (res.code === 0 && res.data) {
        // 检查是否是自己的名片
        const isOwner = app.globalData.userInfo && 
          app.globalData.cardInfo && 
          app.globalData.cardInfo.id == this.data.cardId;
        
        // 处理字段名不一致的问题，统一转为驼峰命名
        const cardData = res.data;
        // 检查是否有company_code字段但没有companyCode字段的情况
        if (cardData.company_code !== undefined && cardData.companyCode === undefined) {
          cardData.companyCode = cardData.company_code;
          console.log("从company_code转换为companyCode:", cardData.companyCode);
        }
        
        console.log("是否是自己的名片:", isOwner);
        console.log("当前名片的companyCode:", cardData.companyCode);
        
        this.setData({
          cardInfo: cardData,
          isCollected: cardData.isCollected,
          isOwner: isOwner
        })

        // 尝试生成分享图
        this.generateShareImage(cardData);

        // 如果有企业编码，获取企业信息
        if (cardData.companyCode) {
          this.loadCompanyInfo(cardData.companyCode);
        }
        
        // 缓存数据
        if (!wx.cardDetailCache.personal[this.data.cardId]) {
          wx.cardDetailCache.personal[this.data.cardId] = {};
        }
        wx.cardDetailCache.personal[this.data.cardId].cardInfo = cardData;
        wx.cardDetailCache.personal[this.data.cardId].isOwner = isOwner;
        
        // 检查是否有待处理的目标标签页
        this.checkPendingTargetTab();
        
        this.setData({
          isLoading: false
        })
        wx.hideLoading()
        
        return cardData;
      } else {
        console.error("获取名片详情失败:", res);
        wx.showToast({
          title: '获取名片信息失败',
          icon: 'none'
        })
        this.setData({
          isLoading: false
        })
        wx.hideLoading()
        return null;
      }
    }).catch(err => {
      console.error("获取名片详情请求失败:", err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
      this.setData({
        isLoading: false
      })
      wx.hideLoading()
      return null;
    })
  },

  // 加载企业信息
  loadCompanyInfo(companyCode) {
    console.log("开始加载企业信息, companyCode:", companyCode);
    
    const company = require('../../utils/company.js');
    company.getCompanyInfo(companyCode).then(res => {
      console.log("获取企业信息结果:", res);
      
      if (res && res.companyCode) {
        // 确保分享图字段存在
        const companyData = {
          ...res,
          // 如果没有分享图，使用默认图片
          enterpriseShareImage: res.enterpriseShareImage || '',
          productsShareImage: res.productsShareImage || ''
        };
        
        // 更新页面数据
        this.setData({
          companyInfo: companyData
        });
        
        // 更新缓存
        if (wx.cardDetailCache.personal[this.data.cardId]) {
          wx.cardDetailCache.personal[this.data.cardId].companyInfo = companyData;
        }
        
        // 更新企业和产品中心的自定义名称
        this.setData({
          enterpriseName: res.enterpriseName || '企业',
          productsName: res.productsName || '产品中心'
        });
        
        console.log("已更新企业自定义名称:", res.enterpriseName, "产品中心自定义名称:", res.productsName);
        console.log("企业分享图:", companyData.enterpriseShareImage, "产品中心分享图:", companyData.productsShareImage);

        // 检查该企业是否有产品
        this.checkCompanyProducts(companyCode);
      }
    }).catch(err => {
      console.error("获取企业信息失败:", err);
    });
  },

  // 检查企业是否有产品
  checkCompanyProducts(companyCode) {
    console.log("检查企业产品列表, companyCode:", companyCode);

    // 使用静默请求，不显示错误提示
    this.silentCheckProducts(companyCode);
  },

  // 静默检查产品列表（不显示错误提示）
  silentCheckProducts(companyCode) {
    const app = getApp();
    const baseUrl = 'https://zl.sdtaa.com/api/v1';

    wx.request({
      url: `${baseUrl}/products?companyCode=${companyCode}`,
      method: 'GET',
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log("静默获取企业产品列表结果:", res);

        if (res.statusCode === 200 && res.data && res.data.code === 0 && res.data.data) {
          // 检查产品列表，数据结构是 { total, page, pageSize, list }
          const productList = res.data.data.list || [];
          const hasProducts = productList.length > 0;
          console.log("企业是否有产品:", hasProducts, "产品数量:", productList.length);
          console.log("产品列表数据结构:", res.data.data);

          this.setData({
            hasProducts: hasProducts
          });
        } else {
          console.log("获取产品列表失败或无产品，设置为无产品");
          this.setData({
            hasProducts: false
          });
        }
      },
      fail: (err) => {
        console.log("静默检查企业产品失败，设置为无产品:", err);
        this.setData({
          hasProducts: false
        });
      }
    });
  },

  // 收藏名片
  collectCard() {
    // 检查登录状态
    if (!app.globalData.userInfo) {
      this.loginAndCollect()
      return
    }
    
    // 已收藏则不再处理
    if (this.data.isCollected || this.data.isCollecting) {
      return
    }
    
    // 检查是否是自己的名片
    if (this.data.isOwner) {
      wx.showToast({
        title: '不能收藏自己的名片',
        icon: 'none'
      })
      return
    }
    
    this.setData({
      isCollecting: true
    })
    
    wx.showLoading({
      title: '收藏中',
    })
    
    const collection = require('../../utils/collection.js')
    collection.collectCard(this.data.cardId).then(res => {
      if (res.code === 0) {
        this.setData({
          isCollected: true
        })
        
        // 更新缓存中的收藏状态
        if (wx.cardDetailCache.personal[this.data.cardId]) {
          wx.cardDetailCache.personal[this.data.cardId].isCollected = true;
        }
        
        wx.showToast({
          title: '已收藏到名片夹',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: res.message || '收藏失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('收藏名片失败', err)
      wx.showToast({
        title: err.message || '收藏失败，请重试',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({
        isCollecting: false
      })
      wx.hideLoading()
    })
  },

  // 跳转到AI分身页面
  goToAiAvatar() {
    if (!this.data.cardInfo || !this.data.cardInfo.aiAvatarUrl) {
      wx.showToast({
        title: 'AI分身链接未设置',
        icon: 'none'
      })
      return
    }

    console.log('跳转到AI分身页面，URL:', this.data.cardInfo.aiAvatarUrl);

    wx.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(this.data.cardInfo.aiAvatarUrl)}`
    })
  },

  // 登录并收藏
  loginAndCollect() {
    // 显示登录提示
    wx.showModal({
      title: '需要登录',
      content: '收藏名片需要先登录',
      confirmText: '去登录',
      confirmColor: '#3E7FFF',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/index'
          });
        }
      }
    });
  },

  // 拨打电话
  makePhoneCall() {
    if (this.data.cardInfo.mobile) {
      wx.makePhoneCall({
        phoneNumber: this.data.cardInfo.mobile
      })
    }
  },

  // 拨打座机
  makePhoneCallLandline() {
    if (this.data.cardInfo.phone) {
      wx.makePhoneCall({
        phoneNumber: this.data.cardInfo.phone
      })
    }
  },

  // 复制微信号
  copyWechat() {
    if (this.data.cardInfo.wechat) {
      wx.setClipboardData({
        data: this.data.cardInfo.wechat,
        success: () => {
          wx.showToast({
            title: '微信号已复制',
            icon: 'success'
          })
        }
      })
    }
  },

  // 发送邮件
  sendEmail() {
    if (this.data.cardInfo.email) {
      wx.setClipboardData({
        data: this.data.cardInfo.email,
        success: () => {
          wx.showToast({
            title: '邮箱已复制',
            icon: 'success'
          })
        }
      })
    }
  },

  // 查看网站
  openWebsite() {
    if (this.data.cardInfo.website) {
      // 处理网址，确保以http或https开头
      let website = this.data.cardInfo.website
      if (!/^https?:\/\//i.test(website)) {
        website = 'https://' + website
      }
      
      wx.setClipboardData({
        data: website,
        success: () => {
          wx.showToast({
            title: '网址已复制',
            icon: 'success'
          })
        }
      })
    }
  },

  // 复制地址
  copyAddress() {
    if (this.data.cardInfo.address) {
      wx.setClipboardData({
        data: this.data.cardInfo.address,
        success: () => {
          wx.showToast({
            title: '地址已复制',
            icon: 'success'
          })
        }
      })
    }
  },

  // 生成分享图
  generateShareImage(cardInfo) {
    console.log("开始生成分享图:", cardInfo);
    
    if (!cardInfo) {
      console.error('缺少名片信息，无法生成分享图');
      return Promise.reject(new Error('缺少名片信息'));
    }
    
    // 引入分享图生成工具
    const shareImage = require('../../utils/share-image.js');
    
    // 如果已经有分享图路径，且不是强制重新生成，则直接返回
    if (this.data.shareImagePath) {
      console.log('使用已缓存的分享图:', this.data.shareImagePath);
      return Promise.resolve(this.data.shareImagePath);
    }
    
    // 检查是否有缓存的分享图
    const cachedImagePath = shareImage.getShareImageFromCache(cardInfo.id);
    if (cachedImagePath) {
      console.log('使用缓存的分享图:', cachedImagePath);
      this.setData({
        shareImagePath: cachedImagePath,
        isGeneratingShareImage: false
      });
      return Promise.resolve(cachedImagePath);
    }
    
    // 设置正在生成分享图状态
    this.setData({
      isGeneratingShareImage: true
    });
    
    // 显示加载中提示
    wx.showLoading({
      title: '准备图片',
      mask: true
    });
    
    // 生成分享图
    return shareImage.generateCardShareImage(cardInfo)
      .then(tempFilePath => {
        console.log('分享图生成成功:', tempFilePath);
        this.setData({
          shareImagePath: tempFilePath,
          isGeneratingShareImage: false
        });
        wx.hideLoading();
        return tempFilePath;
      })
      .catch(err => {
        console.error('分享图生成失败:', err);
        // 确保释放生成状态，即使失败也允许用户点击分享
        this.setData({
          isGeneratingShareImage: false
        });
        wx.hideLoading();
        return Promise.reject(err);
      });
  },

  // 分享名片
  onShareAppMessage() {
    // 如果没有分享图路径，但有名片信息，尝试生成
    if (!this.data.shareImagePath && this.data.cardInfo) {
      this.generateShareImage(this.data.cardInfo);
    }
    
    // 获取分享图路径，如果还未生成完成，则使用头像或默认图片
    const imageUrl = this.data.shareImagePath || 
      (this.data.cardInfo && this.data.cardInfo.avatar ? 
        this.data.cardInfo.avatar : 
        'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-share.png');
    
    return {
      title: this.data.cardInfo ? `${this.data.cardInfo.name}的名片` : '智链名片',
      path: `/pages/card-detail/index?id=${this.data.cardId}&fromShare=true`,
      imageUrl: imageUrl
    }
  },

  // 跳转到个人页面 - 更新方法
  goToPersonal() {
    this.setData({
      selected: 'personal',
      showEnterpriseWebView: false,
      showProductsWebView: false
    })
    // 保存当前选中的标签页到本地存储
    wx.setStorageSync('card_detail_selected_tab', 'personal')
    
    // 如果当前已经在名片详情页，只是切换标签而不跳转
    if (getCurrentPages().slice(-1)[0].route === 'pages/card-detail/index') {
      // 已经在名片详情页，不需要跳转
      console.log("已在个人页面");
      return
    }
    
    // 从其他页面返回当前名片详情页
    if (this.data.cardId) {
      wx.redirectTo({
        url: `/pages/card-detail/index?id=${this.data.cardId}`
      })
    }
  },

  // 查看企业详情
  viewEnterpriseDetails() {
    if (!this.data.cardId) {
      return;
    }
    
    // 判断是否正在生成分享图
    if (this.data.isGeneratingShareImage) {
      console.log("正在生成分享图，等待完成后再跳转");
      wx.showLoading({
        title: '准备中...',
        mask: true
      });
      
      // 如果正在生成分享图，等待其完成后再跳转
      const shareImageTask = this.generateShareImage(this.data.cardInfo);
      shareImageTask
        .then(() => {
          wx.hideLoading();
          this.performEnterpriseNavigation();
        })
        .catch(err => {
          console.error("分享图生成失败，但仍继续跳转:", err);
          wx.hideLoading();
          this.performEnterpriseNavigation();
        });
    } else {
      // 如果没有正在生成分享图，直接跳转
      this.performEnterpriseNavigation();
    }
  },
  
  // 实际执行企业页面跳转
  performEnterpriseNavigation() {
    // 如果是从名片夹进入的分享名片，或者是别人的名片，都使用当前名片的企业信息
    if (this.data.fromShared || !this.data.isOwner) {
      // 使用当前名片的企业信息
      if (this.data.companyInfo && this.data.companyInfo.companyCode) {
        wx.navigateTo({
          url: `/pages/enterprise/index?companyCode=${this.data.companyInfo.companyCode}&fromShare=true&cardId=${this.data.cardId}&fromCardDetail=true`
        });
      } else {
        wx.showToast({
          title: '企业信息不完整',
          icon: 'none',
          duration: 2000
        });
      }
    } else {
      // 如果是自己的名片且不是从分享进入，使用用户自己的企业信息
      wx.navigateTo({
        url: `/pages/enterprise/index?cardId=${this.data.cardId}&fromCardDetail=true`
      });
    }
  },

  // 查看产品中心详情
  viewProductsDetails() {
    if (!this.data.cardId) {
      return;
    }
    
    // 判断是否正在生成分享图
    if (this.data.isGeneratingShareImage) {
      console.log("正在生成分享图，等待完成后再跳转");
      wx.showLoading({
        title: '准备中...',
        mask: true
      });
      
      // 如果正在生成分享图，等待其完成后再跳转
      const shareImageTask = this.generateShareImage(this.data.cardInfo);
      shareImageTask
        .then(() => {
          wx.hideLoading();
          this.performProductsNavigation();
        })
        .catch(err => {
          console.error("分享图生成失败，但仍继续跳转:", err);
          wx.hideLoading();
          this.performProductsNavigation();
        });
    } else {
      // 如果没有正在生成分享图，直接跳转
      this.performProductsNavigation();
    }
  },
  
  // 实际执行产品中心页面跳转
  performProductsNavigation() {
    // 统一使用products/index页面，和企业页面逻辑一致
    if (this.data.fromShared || !this.data.isOwner) {
      // 如果是从名片夹进入的分享名片，或者是别人的名片，使用当前名片的企业信息
      if (this.data.companyInfo && this.data.companyInfo.companyCode) {
        wx.navigateTo({
          url: `/pages/products/index?companyCode=${this.data.companyInfo.companyCode}&fromShare=true&cardId=${this.data.cardId}&productsName=${encodeURIComponent(this.data.productsName || '产品中心')}`
        });
      } else {
        wx.showToast({
          title: '企业信息不完整',
          icon: 'none',
          duration: 2000
        });
      }
    } else {
      // 如果是自己的名片，直接跳转到产品中心页面（不传递参数，使用用户自己的企业信息）
      wx.navigateTo({
        url: `/pages/products/index?cardId=${this.data.cardId}&fromCardDetail=true&productsName=${encodeURIComponent(this.data.productsName || '产品中心')}`
      });
    }
  },

  // 检查名片收藏状态
  checkCollectionStatus(cardId) {
    console.log("检查名片收藏状态, cardId:", cardId);
    const card = require('../../utils/card.js');
    const collection = require('../../utils/collection.js');
    
    // 显示加载中以提供更好的用户体验
    wx.showLoading({
      title: '检查状态',
      mask: true
    });
    
    collection.checkCollectionStatus(cardId)
      .then(res => {
        if (res.code === 0) {
          const isCollected = res.data;
          console.log("收藏状态检查结果:", isCollected);
          
          // 始终更新状态，不考虑之前的状态
          this.setData({
            isCollected: isCollected
          });
          
          // 更新缓存
          if (wx.cardDetailCache.personal[cardId]) {
            wx.cardDetailCache.personal[cardId].isCollected = isCollected;
          } else {
            // 如果没有缓存，创建一个基础的缓存数据结构
            wx.cardDetailCache.personal[cardId] = {
              cardInfo: this.data.cardInfo || null,
              isCollected: isCollected,
              isOwner: this.data.isOwner || false,
              companyInfo: this.data.companyInfo || null
            };
          }
        }
      })
      .catch(err => {
        console.error("检查收藏状态失败:", err);
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 根据目标标签页自动切换
  switchToTargetTab(targetTab) {
    console.log("准备切换到标签页:", targetTab);
    
    // 先记录下需要跳转的标签，确保数据加载完成后仍能正确跳转
    wx.setStorageSync('card_detail_selected_tab', targetTab);
    
    if (targetTab === 'enterprise') {
      // 确保cardInfo已加载完成，再跳转
      if (this.data.cardInfo && !this.data.isLoading) {
        // 如果正在生成分享图，等待其完成后再跳转
        if (this.data.isGeneratingShareImage) {
          console.log("等待分享图生成完成后再跳转到企业页面");
          wx.showLoading({
            title: '准备中...',
            mask: true
          });
          
          const shareImageTask = this.generateShareImage(this.data.cardInfo);
          shareImageTask
            .then(() => {
              wx.hideLoading();
              this.viewEnterpriseDetails();
            })
            .catch(err => {
              console.error("分享图生成失败，但仍继续跳转:", err);
              wx.hideLoading();
              this.viewEnterpriseDetails();
            });
        } else {
          this.viewEnterpriseDetails();
        }
      } else {
        console.log("等待数据加载完成后再跳转到企业页面");
        // 设置标志，在数据加载完成后跳转
        this.setData({
          pendingTargetTab: 'enterprise'
        });
      }
    } else if (targetTab === 'products') {
      // 确保cardInfo已加载完成，再跳转
      if (this.data.cardInfo && !this.data.isLoading) {
        // 如果正在生成分享图，等待其完成后再跳转
        if (this.data.isGeneratingShareImage) {
          console.log("等待分享图生成完成后再跳转到产品中心页面");
          wx.showLoading({
            title: '准备中...',
            mask: true
          });
          
          const shareImageTask = this.generateShareImage(this.data.cardInfo);
          shareImageTask
            .then(() => {
              wx.hideLoading();
              this.viewProductsDetails();
            })
            .catch(err => {
              console.error("分享图生成失败，但仍继续跳转:", err);
              wx.hideLoading();
              this.viewProductsDetails();
            });
        } else {
          this.viewProductsDetails();
        }
      } else {
        console.log("等待数据加载完成后再跳转到产品页面");
        // 设置标志，在数据加载完成后跳转
        this.setData({
          pendingTargetTab: 'products'
        });
      }
    } else {
      this.goToPersonal();
    }
  },

  // 检查用户是否已登录且有名片
  checkUserHasCard() {
    console.log("开始检查用户名片状态");
    
    const card = require('../../utils/card.js');
    
    // 调用card.js中更新后的方法，该方法会请求后端API
    return card.checkUserHasCard().then(res => {
      if (res.code === 0 && res.data) {
        const { isLoggedIn, hasCardInfo, cardInfo } = res.data;
        
        console.log("用户登录状态:", isLoggedIn);
        console.log("用户名片状态:", hasCardInfo);
        
        // 更新页面数据
        this.setData({
          hasMyCard: hasCardInfo
        });
        
        // 如果返回了名片信息，更新全局数据
        if (cardInfo) {
          app.globalData.cardInfo = cardInfo;
          wx.setStorageSync('cardInfo', cardInfo);
        }
        
        return hasCardInfo;
      } else {
        console.error("检查用户名片状态失败:", res.message);
        this.setData({
          hasMyCard: false
        });
        return false;
      }
    }).catch(err => {
      console.error("检查用户名片状态出错:", err);
      this.setData({
        hasMyCard: false
      });
      return false;
    });
  },
  
  // 处理点击创建/回到我的名片按钮
  goToMyCard() {
    // 显示加载中提示
    wx.showLoading({
      title: '加载中',
    });
    
    // 使用card.js中的方法检查用户是否有名片
    const card = require('../../utils/card.js');
    
    card.checkUserHasCard().then(res => {
      wx.hideLoading();
      
      if (res.code === 0) {
        const isLoggedIn = res.data.isLoggedIn;
        const hasCardInfo = res.data.hasCardInfo;
        
        if (!isLoggedIn) {
          // 用户未登录，弹出确认框
          wx.showModal({
            title: '需要登录',
            content: '您需要登录后才能使用此功能',
            confirmText: '去登录',
            confirmColor: '#3E7FFF',
            success: (res) => {
              if (res.confirm) {
                // 用户点击确定，跳转到登录页面
                wx.navigateTo({
                  url: '/pages/login/index'
                });
              }
            }
          });
        } else if (!hasCardInfo) {
          // 用户已登录但没有名片，跳转到创建名片页面
          console.log("用户已登录但没有名片，跳转到创建名片页面");
          wx.navigateTo({
            url: '/pages/personal/edit'
          });
        } else {
          // 用户已登录且有名片，跳转到个人主页
          console.log("用户已登录且有名片，跳转到个人主页");
          wx.switchTab({
            url: '/pages/personal/index',
            fail: (err) => {
              console.error("跳转到个人主页失败:", err);
              // 如果跳转失败，尝试使用重定向
              wx.redirectTo({
                url: '/pages/personal/index'
              });
            }
          });
        }
      } else {
        console.error("检查用户名片状态失败:", res.message);
        // 出错时默认跳转到个人主页，让用户自己选择操作
        wx.switchTab({
          url: '/pages/personal/index'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error("检查用户名片状态出错:", err);
      // 出错时默认跳转到个人主页，让用户自己选择操作
      wx.switchTab({
        url: '/pages/personal/index'
      });
    });
  },

  // 检查是否有待处理的标签页跳转
  checkPendingTargetTab() {
    const pendingTab = this.data.pendingTargetTab;
    if (pendingTab) {
      this.switchToTargetTab(pendingTab);
      this.setData({
        pendingTargetTab: null
      });
    }
  }
}) 