.card-container {
  padding: 0;
  width: 100%;
}

.card-body {
  background: linear-gradient(135deg, #3E7FFF, #72AAFF);
  padding: 24rpx;
  position: relative;
  overflow: hidden;
  border-radius: 20rpx;
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 卡片背景装饰 */
.card-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="%234B8BFF"/><rect x="0" y="0" width="50" height="50" fill="%233E7FFF"/><rect x="50" y="50" width="50" height="50" fill="%233E7FFF"/></svg>');
  background-size: 20rpx 20rpx;
  opacity: 0.1;
  z-index: 0;
}

/* 基本信息 */
.card-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  position: relative;
  z-index: 1;
  align-items: flex-start;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.2); /* 默认头像背景颜色 */
}

.basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-right: 16rpx;
}

.name {
  font-size: 34rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 6rpx;
}

.company {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4rpx;
}

.position {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.industry {
  font-size: 24rpx;
  color: #3E7FFF;
  background-color: rgba(62, 127, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 操作按钮 */
.action-bar {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 0;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 10rpx 30rpx;
}

.action-button::after {
  content: '';
  position: absolute;
  right: 0;
  top: 10%;
  height: 80%;
  width: 1rpx;
  background-color: rgba(0, 0, 0, 0.05);
}

.action-button:last-child::after {
  display: none;
}

.icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 10rpx;
}

.action-button text {
  font-size: 24rpx;
  color: #666666;
}

.edit-icon {
  background-image: url('/images/icons/edit.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.share-icon {
  background-image: url('/images/icons/share.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.collect-icon {
  background-image: url('/images/icons/collect.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 联系方式 */
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  padding-left: 16rpx;
  border-left: 6rpx solid #3E7FFF;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  height: 40rpx;
  line-height: 40rpx;
}

.contact-section {
  margin-top: 16rpx;
  position: relative;
  z-index: 1;
}

.contact-item {
  display: flex;
  padding: 6rpx 0;
  align-items: center;
  margin-bottom: 4rpx;
}

.contact-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
  filter: brightness(0) invert(1);
}

.contact-value {
  font-size: 22rpx;
  color: #ffffff;
  word-break: break-all;
}

.mobile-icon, .phone-icon {
  background-image: url('/images/common/phone.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.wechat-icon {
  background-image: url('/images/common/wechat.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.email-icon {
  background-image: url('/images/common/email.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.website-icon {
  background-image: url('/images/common/website.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.address-icon {
  background-image: url('/images/common/address.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 业务简介 */
.introduction-section {
  margin-top: 20rpx;
  position: relative;
  z-index: 1;
}

.introduction-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.8;
  text-align: justify;
  background-color: rgba(62, 127, 255, 0.03);
  padding: 20rpx;
  border-radius: 12rpx;
}

/* 共享卡片样式 */
.share-card-container {
  width: 100%;
  height: 400rpx; /* 用于分享卡片的预设高度 */
  position: relative;
}

/* 用于扫描后的外部视图 */
.card-scan-view {
  background: linear-gradient(135deg, #3E7FFF, #72AAFF);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
} 