const app = getApp()

Page({
  data: {
    cardList: [],
    page: 1,
    pageSize: 20,
    total: 0,
    isLoading: false,
    hasMore: true
  },

  onLoad() {
    this.loadCardList(true)
  },

  onShow() {
    // 显示自定义tabBar，设置选中状态为名片夹(1)
    setTimeout(() => {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 1 // 选中名片夹
        });
      }
    }, 100);
  },

  onPullDownRefresh() {
    this.setData({
      page: 1,
      cardList: []
    })
    this.loadCardList(true).then(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadCardList()
    }
  },

  // 格式化日期，只保留年月日
  formatDate(dateString) {
    if (!dateString) return '';
    // 处理日期字符串，只保留年月日
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  // 加载名片夹列表
  loadCardList(showLoading = false) {
    if (this.data.isLoading) return Promise.resolve()
    
    this.setData({
      isLoading: true
    })
    
    if (showLoading) {
      wx.showLoading({
        title: '加载中',
      })
    }
    
    const card = require('../../utils/card.js')
    return card.getCardFolder(this.data.page, this.data.pageSize).then(res => {
      if (res.code === 0 && res.data) {
        // 处理日期格式，只保留年月日
        const formattedList = res.data.list.map(item => {
          return {
            ...item,
            collectedAt: this.formatDate(item.collectedAt)
          };
        });

        // 追加数据
        const newList = this.data.page === 1 ? formattedList : [...this.data.cardList, ...formattedList]

        this.setData({
          cardList: newList,
          total: res.data.total,
          hasMore: newList.length < res.data.total,
          page: this.data.page + 1
        })
      }
      return res
    }).catch(err => {
      console.error('获取名片夹列表失败', err)

      // 如果是401错误，说明token已失效，跳转到个人页面
      if (err && (err.code === 401 || err.statusCode === 401)) {
        console.log('名片夹获取失败，token已失效，跳转到个人页面')
        wx.showToast({
          title: '登录已过期，请重新登录',
          icon: 'none',
          duration: 2000
        })
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/personal/index'
          })
        }, 2000)
      } else {
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      }

      return Promise.reject(err)
    }).finally(() => {
      this.setData({
        isLoading: false
      })
      if (showLoading) {
        wx.hideLoading()
      }
    })
  },

  // 查看名片详情
  viewCardDetail(e) {
    const { id } = e.currentTarget.dataset
    // 名片夹里的名片都是分享后收藏的，需要添加fromShared标识
    wx.navigateTo({
      url: `/pages/card-detail/index?id=${id}&fromShared=true`,
    })
  },

  // 长按删除名片
  onLongPress(e) {
    const { collectionId, index } = e.currentTarget.dataset

    wx.showModal({
      title: '提示',
      content: '确定删除此名片吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteCard(collectionId, index)
        }
      }
    })
  },

  // 删除名片
  deleteCard(collectionId, index) {
    wx.showLoading({
      title: '删除中',
    })
    
    const card = require('../../utils/card.js')
    card.removeCollection(collectionId).then(res => {
      if (res.code === 0) {
        // 从列表中移除
        const cardList = [...this.data.cardList]
        cardList.splice(index, 1)
        
        this.setData({
          cardList,
          total: this.data.total - 1
        })
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: res.message || '删除失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('删除名片失败', err)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  }
}) 