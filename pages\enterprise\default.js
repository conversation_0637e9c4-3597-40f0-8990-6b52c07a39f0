const app = getApp()

Page({
  data: {
    companyInfo: null,
    companyDetail: {
      name: '智链科技',
      logo: '/images/company/default-logo.png',
      slogan: '连接人与企业的智慧纽带',
      intro: '智链科技是一家专注于提供企业数字化名片解决方案的科技公司，致力于帮助企业和个人建立高效的人脉管理和客户关系管理体系。',
      advantage: [
        {
          title: '便捷高效',
          desc: '一键生成电子名片，随时随地管理个人及企业形象'
        },
        {
          title: '品牌定制',
          desc: '企业专属品牌形象定制，突显企业个性与文化'
        },
        {
          title: '云端共享',
          desc: '团队成员名片统一管理，客户资源共享协作'
        }
      ],
      address: '北京市朝阳区科技园区88号智链大厦',
      phone: '010-88889999',
      email: '<EMAIL>'
    },
    fromCardDetail: false,
    cardId: null
  },

  onLoad(options) {
    // 从用户信息获取企业信息
    if (app.globalData.userInfo && app.globalData.userInfo.companyCode) {
      this.getCompanyInfo()
    }
    
    // 检查是否有从URL传来的参数
    if (options.cardId && options.fromCardDetail) {
      this.setData({
        fromCardDetail: true,
        cardId: options.cardId
      })
      return
    }
    
    // 检查eventChannel是否存在数据传递
    const eventChannel = this.getOpenerEventChannel()
    if (eventChannel) {
      try {
        eventChannel.on('acceptDataFromCardDetail', data => {
          if (data && data.fromCardDetail) {
            this.setData({
              fromCardDetail: true,
              cardId: data.cardId
            })
          }
        })
      } catch (err) {
        console.log('获取事件通道数据失败', err)
      }
    }
    
    // 从本地存储获取名片ID
    const currentCardId = wx.getStorageSync('current_card_id')
    if (currentCardId) {
      this.setData({
        fromCardDetail: true,
        cardId: currentCardId
      })
    }
  },

  getCompanyInfo() {
    const company = require('../../utils/company.js')
    company.getCompanyInfo().then(res => {
      if (res.code === 0 && res.data) {
        this.setData({
          companyInfo: res.data
        })
      }
    }).catch(err => {
      console.error('获取企业信息失败', err)
    })
  },

  makePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: this.data.companyDetail.phone,
    })
  },

  copyEmail() {
    wx.setClipboardData({
      data: this.data.companyDetail.email,
      success() {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        })
      }
    })
  },

  viewLocation() {
    wx.showToast({
      title: '位置查看功能开发中',
      icon: 'none'
    })
  }
}) 