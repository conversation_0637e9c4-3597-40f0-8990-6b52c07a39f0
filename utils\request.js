const app = getApp();

// 退避等待时间控制
let backoffDelay = 1000; // 初始等待时间1秒
let lastRequestTime = 0; // 上次请求时间
const MAX_BACKOFF_DELAY = 30000; // 最大等待时间30秒

/**
 * 封装请求方法
 * @param {Object} options - 请求配置
 * @param {string} options.url - 请求URL，不含域名部分
 * @param {string} [options.method='GET'] - 请求方法
 * @param {Object} [options.data={}] - 请求数据
 * @param {boolean} [options.auth=true] - 是否需要验证
 * @returns {Promise} - 返回Promise对象
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 当前时间
    const now = Date.now();
    
    // 如果距离上次请求时间太短并且是登录请求，则延迟执行
    if (options.url.includes('/auth/login') && now - lastRequestTime < backoffDelay) {
      const waitTime = backoffDelay;
      backoffDelay = Math.min(backoffDelay * 2, MAX_BACKOFF_DELAY); // 指数增长但不超过最大值
      
      console.log(`登录请求过于频繁，将延迟${waitTime}ms后重试`);
      
      wx.showToast({
        title: '请求过于频繁，请稍候再试',
        icon: 'none',
        duration: 2000
      });
      
      setTimeout(() => {
        request(options).then(resolve).catch(reject);
      }, waitTime);
      
      return;
    }
    
    // 更新最后请求时间
    lastRequestTime = now;
    
    const token = app.globalData.token || wx.getStorageSync('token') || '';
    const header = options.auth !== false && token ? { 'Authorization': `Bearer ${token}` } : {};
    
    // 添加通用请求头
    if (options.method === 'POST' || options.method === 'PUT') {
      header['Content-Type'] = 'application/json';
    }
    
    const fullUrl = `${app.globalData.baseUrl}${options.url}`;
    console.log(`发送请求: ${options.method || 'GET'} ${fullUrl}`);
    console.log('请求头:', header);
    console.log('请求数据:', options.data || {});
    
    wx.request({
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data || {},
      header,
      success: (res) => {
        const { statusCode, data } = res;
        console.log(`请求响应: ${options.method || 'GET'} ${fullUrl}, 状态码: ${statusCode}`);
        console.log('响应数据:', data);
        
        // 处理429错误（请求过多）
        if (statusCode === 429) {
          backoffDelay = Math.min(backoffDelay * 2, MAX_BACKOFF_DELAY); // 指数增长
          
          console.log(`收到429状态码，将延迟${backoffDelay}ms后重试`);
          
          wx.showToast({
            title: '请求过于频繁，请稍候再试',
            icon: 'none',
            duration: 2000
          });
          
          // 延迟后自动重试
          setTimeout(() => {
            request(options).then(resolve).catch(reject);
          }, backoffDelay);
          
          return;
        }
        
        // 请求成功时重置退避时间
        if (statusCode === 200 && data.code === 0) {
          backoffDelay = 1000; // 重置为初始值
        }
        
        // 请求成功，但业务错误
        if (statusCode === 200 && data.code !== 0) {
          // token失效，需要重新登录
          if (data.code === 401) {
            console.log('Token失效，清除登录状态');
            // 彻底清理所有登录相关的缓存和状态
            app.globalData.token = '';
            app.globalData.userInfo = null;
            app.globalData.cardInfo = null;
            app.globalData.companyInfo = null;
            app.globalData.cardUpdated = false;

            // 清理本地存储
            wx.removeStorageSync('token');
            wx.removeStorageSync('userInfo');
            wx.removeStorageSync('cardInfo');
            wx.removeStorageSync('companyInfo');

            // 更新TabBar状态为隐藏
            if (app.globalData.tabBarControl) {
              app.globalData.tabBarControl.show = false;
            }

            // 对于401错误直接reject，不显示toast，让调用方处理
            reject(data);
            return;
          }
          
          console.log(`业务错误: code=${data.code}, message=${data.message}`);
          
          wx.showToast({
            title: data.message || '请求失败',
            icon: 'none'
          });
          reject(data);
        } else if (statusCode !== 200) {
          // HTTP错误
          let errorMessage = '请求错误';
          let errorCode = statusCode;

          // 尝试从响应中提取更详细的错误信息
          if (data && data.message) {
            errorMessage = data.message;
            errorCode = data.code || statusCode;
          }

          console.log(`HTTP错误: statusCode=${statusCode}, errorCode=${errorCode}, errorMessage=${errorMessage}`);

          // 特殊处理401未授权错误
          if (statusCode === 401) {
            console.log('HTTP 401错误，清除登录状态');
            // 彻底清理所有登录相关的缓存和状态
            app.globalData.token = '';
            app.globalData.userInfo = null;
            app.globalData.cardInfo = null;
            app.globalData.companyInfo = null;
            app.globalData.cardUpdated = false;

            // 清理本地存储
            wx.removeStorageSync('token');
            wx.removeStorageSync('userInfo');
            wx.removeStorageSync('cardInfo');
            wx.removeStorageSync('companyInfo');

            // 更新TabBar状态为隐藏
            if (app.globalData.tabBarControl) {
              app.globalData.tabBarControl.show = false;
            }
          } else {
            // 其他HTTP错误显示提示
            wx.showToast({
              title: errorMessage,
              icon: 'none'
            });
          }

          reject({
            code: errorCode,
            message: errorMessage
          });
        } else {
          // 成功
          console.log('请求成功，返回数据');
          resolve(data.data);
        }
      },
      fail: (err) => {
        console.error(`请求失败: ${options.method || 'GET'} ${fullUrl}`, err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

// GET请求
const get = (url, data = {}, auth = true) => {
  console.log(`发起GET请求: ${url}, 参数:`, data, `是否需要认证: ${auth}`);
  return request({ url, data, method: 'GET', auth });
};

// POST请求
const post = (url, data = {}, auth = true) => {
  console.log(`发起POST请求: ${url}, 参数:`, data, `是否需要认证: ${auth}`);
  return request({ url, data, method: 'POST', auth });
};

// PUT请求
const put = (url, data = {}, auth = true) => {
  console.log(`发起PUT请求: ${url}, 参数:`, data, `是否需要认证: ${auth}`);
  return request({ url, data, method: 'PUT', auth });
};

// DELETE请求
const del = (url, data = {}, auth = true) => {
  console.log(`发起DELETE请求: ${url}, 参数:`, data, `是否需要认证: ${auth}`);
  return request({ url, data, method: 'DELETE', auth });
};

module.exports = {
  request,
  get,
  post,
  put,
  delete: del
}; 