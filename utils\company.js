const app = getApp();
const { get, put } = require('./request');

/**
 * 获取当前用户关联的企业信息
 * @param {string} companyCode - 可选的企业编码，如果提供则获取指定企业信息
 * @returns {Promise} 企业信息promise
 */
const getCompanyInfo = async (companyCode) => {
  try {
    // 如果提供了companyCode，添加到查询参数，并且无需认证
    const url = companyCode ? `/company/info?companyCode=${companyCode}` : '/company/info';
    const auth = companyCode ? false : true; // 有companyCode参数时不需要认证
    console.log(`请求企业信息API, URL: ${url}, companyCode: ${companyCode || '未提供'}, 是否需要认证: ${auth}`);
    
    const response = await get(url, {}, auth);
    
    // 打印原始响应
    console.log(`API返回原始数据 (companyCode=${companyCode || '未提供'}):`, response);
    
    // 判断返回数据格式
    let companyData = null;
    
    // 如果response直接就是企业信息对象（有companyCode字段）
    if (response && typeof response === 'object' && response.companyCode) {
      companyData = response;
      console.log(`直接返回企业信息对象 (companyCode=${companyCode || '未提供'})`);
    } 
    // 如果response是标准格式（有code和data字段）
    else if (response && response.code === 0 && response.data) {
      companyData = response.data;
      console.log(`标准格式返回企业信息 (companyCode=${companyCode || '未提供'})`);
    }
    
    // 只有获取到有效的企业信息时才更新全局状态
    if (!companyCode && companyData) {
      console.log("获取到当前用户企业信息，更新全局状态:", companyData);
      app.globalData.companyInfo = companyData;
    } else if (companyCode && companyData) {
      console.log(`获取到指定企业信息 (companyCode=${companyCode}), 不更新全局状态`);
    } else {
      console.log(`未获取到企业信息 (companyCode=${companyCode || '未提供'})`);
    }
    
    return companyData;
  } catch (err) {
    console.error(`获取企业信息失败 (companyCode=${companyCode || '未提供'})`, err);
    return null;
  }
};

/**
 * 获取企业页面路径
 * @returns {string} 企业页面路径
 */
const getEnterprisePage = async () => {
  let response;
  
  if (app.globalData.companyInfo) {
    // 如果已有全局企业信息，直接使用
    response = app.globalData.companyInfo;
  } else {
    // 否则请求获取
    response = await getCompanyInfo();
  }
  
  if (!response || !response.enterprisePage) {
    // 没有企业信息或企业页面路径，返回默认页面
    return '/pages/enterprise/default';
  }
  
  return response.enterprisePage;
};

/**
 * 获取产品中心页面路径
 * @returns {string} 产品中心页面路径
 */
const getProductsPage = async () => {
  let response;
  
  if (app.globalData.companyInfo) {
    // 如果已有全局企业信息，直接使用
    response = app.globalData.companyInfo;
  } else {
    // 否则请求获取
    response = await getCompanyInfo();
  }
  
  if (!response || !response.productsPage) {
    // 没有企业信息或产品中心页面路径，返回默认页面
    return '/pages/products/default';
  }
  
  return response.productsPage;
};

/**
 * 更新企业页面路径（仅管理员）
 * @param {Object} data - 更新数据
 * @param {string} data.companyCode - 企业编码
 * @param {string} data.enterprisePage - 企业页面路径
 * @param {string} data.productsPage - 产品中心页面路径
 * @returns {Promise} 更新结果promise
 */
const updateCompanyPages = async (data) => {
  try {
    const result = await put('/company/pages', data);
    // 更新全局企业信息
    if (app.globalData.companyInfo && 
        app.globalData.companyInfo.companyCode === data.companyCode) {
      app.globalData.companyInfo = {
        ...app.globalData.companyInfo,
        enterprisePage: data.enterprisePage,
        productsPage: data.productsPage
      };
    }
    return result;
  } catch (err) {
    console.error('更新企业页面路径失败', err);
    throw err;
  }
};

module.exports = {
  getCompanyInfo,
  getEnterprisePage,
  getProductsPage,
  updateCompanyPages
}; 