.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  text-align: center;
  padding: 40rpx 0;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.products-list {
  padding: 20rpx 0;
}

.product-card {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 320rpx;
  background-color: #eee;
}

.product-info {
  padding: 30rpx;
}

.product-name {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.product-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.product-features {
  padding: 10rpx 0;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.feature-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #3E7FFF;
  margin-right: 16rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #666;
}

.contact-section {
  text-align: center;
  padding: 40rpx 0;
}

.contact-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 30rpx;
}

.contact-btn {
  background-color: #3E7FFF;
  color: #fff;
  font-size: 32rpx;
  width: 320rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #999;
} 