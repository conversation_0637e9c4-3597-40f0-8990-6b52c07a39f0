/**
 * 错误处理中间件
 */

/**
 * 404错误处理
 */
exports.notFound = (req, res, next) => {
  res.status(404).json({
    code: 404,
    message: `接口 ${req.originalUrl} 不存在`,
    data: null
  });
};

/**
 * 通用错误处理
 */
exports.errorHandler = (err, req, res, next) => {
  // 开发环境打印错误堆栈
  console.error(err);

  const statusCode = err.statusCode || 500;
  
  res.status(statusCode).json({
    code: statusCode,
    message: err.message || '服务器内部错误',
    data: null,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

/**
 * 自定义API错误类
 */
exports.ApiError = class ApiError extends Error {
  constructor(statusCode, message) {
    super(message);
    this.statusCode = statusCode;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}; 