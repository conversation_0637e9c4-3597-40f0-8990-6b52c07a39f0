<view class="select-container">
  <view class="select-label">
    <text class="required" wx:if="{{required}}">*</text>
    <text>{{label}}</text>
  </view>
  <view class="select-wrapper" bindtap="onShowPopup">
    <view class="select-value {{!selectedLabel ? 'placeholder' : ''}}">
      {{selectedLabel || placeholder}}
    </view>
    <view class="select-arrow"></view>
  </view>
  
  <!-- 选择弹窗 -->
  <view class="popup-mask" wx:if="{{showPopup}}" bindtap="onClosePopup"></view>
  <view class="popup-container" wx:if="{{showPopup}}">
    <view class="popup-header">
      <view class="popup-title">{{label}}</view>
      <view class="popup-close" bindtap="onClosePopup"></view>
    </view>
    <view class="popup-content">
      <view 
        class="option-item {{item[valueKey] === value ? 'selected' : ''}}" 
        wx:for="{{options}}" 
        wx:key="index" 
        bindtap="onSelectOption" 
        data-index="{{index}}"
      >
        <text>{{item[labelKey]}}</text>
        <view class="selected-icon" wx:if="{{item[valueKey] === value}}"></view>
      </view>
      <view class="empty-tip" wx:if="{{options.length === 0}}">暂无选项</view>
    </view>
  </view>
</view> 