// store/card.js
const app = getApp();
const request = require('../utils/request.js');

/**
 * 获取当前用户的名片信息
 * @returns {Promise} 名片信息请求Promise
 */
const getCardInfo = () => {
  return new Promise((resolve, reject) => {
    if (app.globalData.cardInfo) {
      resolve({
        code: 0,
        message: 'success',
        data: app.globalData.cardInfo
      });
      return;
    }

    request.get('/card').then(res => {
      if (res.code === 0 && res.data) {
        app.globalData.cardInfo = res.data;
      }
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 获取当前用户的所有名片列表
 * @returns {Promise} 名片列表请求Promise
 */
const getAllCards = () => {
  return new Promise((resolve, reject) => {
    request.get('/card/list').then(res => {
      if (res.code === 0 && res.data) {
        // 可以在这里缓存名片列表
        app.globalData.cardList = res.data.list;
      }
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 创建新名片
 * @param {Object} cardData 名片信息对象
 * @returns {Promise} 创建名片请求Promise
 */
const createNewCard = (cardData) => {
  return new Promise((resolve, reject) => {
    request.post('/card/create', cardData).then(res => {
      if (res.code === 0 && res.data) {
        // 如果新创建的名片是默认名片，更新全局名片信息
        if (res.data.is_default) {
          app.globalData.cardInfo = res.data;
          
          // 同步更新用户companyCode
          if (app.globalData.userInfo && res.data.company_code) {
            app.globalData.userInfo.companyCode = res.data.company_code;
            wx.setStorageSync('userInfo', app.globalData.userInfo);
          }
        }
        
        // 刷新名片列表缓存
        if (app.globalData.cardList) {
          app.globalData.cardList.push(res.data);
        }
      }
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 获取指定ID的名片信息
 * @param {Number} id 名片ID
 * @returns {Promise} 名片信息请求Promise
 */
const getCardById = (id) => {
  return request.get(`/card/${id}`);
};

/**
 * 创建/更新名片信息
 * @param {Object} cardData 名片信息对象
 * @returns {Promise} 创建/更新名片请求Promise
 */
const saveCard = (cardData) => {
  return new Promise((resolve, reject) => {
    request.post('/card', cardData).then(res => {
      if (res.code === 0 && res.data) {
        // 更新全局名片信息
        app.globalData.cardInfo = res.data;
        
        // 同步更新用户companyCode
        if (app.globalData.userInfo && res.data.companyCode) {
          app.globalData.userInfo.companyCode = res.data.companyCode;
          wx.setStorageSync('userInfo', app.globalData.userInfo);
        }
        
        // 更新名片列表缓存中的对应名片
        if (app.globalData.cardList) {
          const index = app.globalData.cardList.findIndex(card => card.id === res.data.id);
          if (index !== -1) {
            app.globalData.cardList[index] = res.data;
          }
        }
      }
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 更新指定ID的名片
 * @param {Number} id 名片ID
 * @param {Object} cardData 名片信息对象
 * @returns {Promise} 更新名片请求Promise
 */
const updateCard = (id, cardData) => {
  return new Promise((resolve, reject) => {
    request.put(`/card/${id}`, cardData).then(res => {
      if (res.code === 0 && res.data) {
        // 如果更新的是默认名片，更新全局名片信息
        if (res.data.is_default) {
          app.globalData.cardInfo = res.data;
          
          // 同步更新用户companyCode
          if (app.globalData.userInfo && res.data.company_code) {
            app.globalData.userInfo.companyCode = res.data.company_code;
            wx.setStorageSync('userInfo', app.globalData.userInfo);
          }
        }
        
        // 更新名片列表缓存中的对应名片
        if (app.globalData.cardList) {
          const index = app.globalData.cardList.findIndex(card => card.id === id);
          if (index !== -1) {
            app.globalData.cardList[index] = res.data;
          }
        }
      }
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 设置默认名片
 * @param {Number} id 名片ID
 * @returns {Promise} 设置默认名片请求Promise
 */
const setDefaultCard = (id) => {
  return new Promise((resolve, reject) => {
    request.put(`/card/${id}/default`).then(res => {
      if (res.code === 0 && res.data) {
        // 更新全局名片信息
        app.globalData.cardInfo = res.data;
        
        // 同步更新用户companyCode
        if (app.globalData.userInfo && res.data.company_code) {
          app.globalData.userInfo.companyCode = res.data.company_code;
          wx.setStorageSync('userInfo', app.globalData.userInfo);
        }
        
        // 更新名片列表缓存中所有名片的默认状态
        if (app.globalData.cardList) {
          app.globalData.cardList = app.globalData.cardList.map(card => {
            return {
              ...card,
              is_default: card.id === id
            };
          });
        }
      }
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 删除名片
 * @param {Number} id 名片ID
 * @param {Boolean} force 是否强制删除（用于删除最后一张名片）
 * @returns {Promise} 删除名片请求Promise
 */
const deleteCard = (id, force = true) => {
  return new Promise((resolve, reject) => {
    // 添加force参数到请求中，允许删除最后一张名片
    request.delete(`/card/${id}?force=${force ? 1 : 0}`).then(res => {
      // 修复：处理res可能为null的情况
      // 由于API返回的data可能为null，我们直接检查响应状态
      // 根据日志，响应成功时返回 {code: 0, message: "success", data: null}
      
      // 如果删除的是默认名片，可能需要重新获取当前默认名片
      if (app.globalData.cardInfo && app.globalData.cardInfo.id === id) {
        // 清除当前名片信息，等待下次重新获取
        app.globalData.cardInfo = null;
      }
      
      // 更新名片列表缓存
      if (app.globalData.cardList) {
        app.globalData.cardList = app.globalData.cardList.filter(card => card.id !== id);
      }
      
      // 无论res.data是否为null，都返回res表示成功
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 收藏名片
 * @param {Number} cardId 要收藏的名片ID
 * @returns {Promise} 收藏名片请求Promise
 */
const collectCard = (cardId) => {
  return request.post('/collection', { cardId });
};

/**
 * 获取名片夹列表
 * @param {Number} page 页码
 * @param {Number} pageSize 每页数量
 * @returns {Promise} 名片夹列表请求Promise
 */
const getCardCollection = (page = 1, pageSize = 20) => {
  return request.get('/collection', { page, pageSize });
};

/**
 * 删除收藏的名片
 * @param {Number} collectionId 收藏记录ID
 * @returns {Promise} 删除收藏名片请求Promise
 */
const deleteCollection = (collectionId) => {
  return request.delete(`/collection/${collectionId}`);
};

module.exports = {
  getCardInfo,
  getAllCards,
  createNewCard,
  getCardById,
  saveCard,
  updateCard,
  setDefaultCard,
  deleteCard,
  collectCard,
  getCardCollection,
  deleteCollection
}; 