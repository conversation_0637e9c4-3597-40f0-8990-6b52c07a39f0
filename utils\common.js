/**
 * 格式化时间
 * @param {string|Date} date - 日期字符串或日期对象
 * @param {string} [format='YYYY-MM-DD HH:mm:ss'] - 格式化模板
 * @returns {string} 格式化后的时间字符串
 */
const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return '';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hour = d.getHours();
  const minute = d.getMinutes();
  const second = d.getSeconds();
  
  const padZero = (num) => {
    return num < 10 ? `0${num}` : num;
  };
  
  return format
    .replace(/YYYY/g, year)
    .replace(/MM/g, padZero(month))
    .replace(/DD/g, padZero(day))
    .replace(/HH/g, padZero(hour))
    .replace(/mm/g, padZero(minute))
    .replace(/ss/g, padZero(second));
};

/**
 * 微信原始格式化时间函数 (从util.js合并)
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串，格式为YYYY/MM/DD HH:mm:ss
 */
const formatTime = date => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`;
};

/**
 * 格式化数字为两位数 (从util.js合并)
 * @param {number} n - 需要格式化的数字
 * @returns {string} 格式化后的两位数字字符串
 */
const formatNumber = n => {
  n = n.toString();
  return n[1] ? n : `0${n}`;
};

/**
 * 检查手机号格式是否有效
 * @param {string} mobile - 手机号
 * @returns {boolean} 是否有效
 */
const isValidMobile = (mobile) => {
  return /^1[3-9]\d{9}$/.test(mobile);
};

/**
 * 检查邮箱格式是否有效
 * @param {string} email - 邮箱
 * @returns {boolean} 是否有效
 */
const isValidEmail = (email) => {
  return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email);
};

/**
 * 检查URL格式是否有效
 * @param {string} url - URL
 * @returns {boolean} 是否有效
 */
const isValidUrl = (url) => {
  return /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*\/?$/.test(url);
};

/**
 * 分享名片
 * @param {Object} cardInfo - 名片信息
 * @returns {Object} 分享配置
 */
const shareCard = (cardInfo) => {
  return {
    title: `${cardInfo.name}的名片`,
    path: `/pages/card-detail/index?id=${cardInfo.id}`,
    imageUrl: cardInfo.avatar || '/images/default-share.png'
  };
};

// 导出所有工具函数
module.exports = {
  formatDate,
  formatTime,
  formatNumber,
  isValidMobile,
  isValidEmail,
  isValidUrl,
  shareCard
}; 