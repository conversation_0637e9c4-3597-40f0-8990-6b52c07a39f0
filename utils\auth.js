const app = getApp();
const { get } = require('./request');

/**
 * 检查是否已登录
 * @returns {boolean} 是否已登录
 */
const isLoggedIn = () => {
  return !!app.globalData.token || !!wx.getStorageSync('token');
};

/**
 * 执行登录流程
 * @returns {Promise} 登录结果promise
 */
const doLogin = () => {
  return new Promise((resolve, reject) => {
    app.login((success) => {
      if (success) {
        resolve(app.globalData.userInfo);
      } else {
        reject(new Error('登录失败'));
      }
    });
  });
};

/**
 * 获取用户信息
 * @returns {Promise} 用户信息promise
 */
const getUserInfo = async () => {
  if (!isLoggedIn()) {
    throw new Error('未登录');
  }
  
  try {
    const userInfo = await get('/auth/user');
    // 更新全局用户信息
    app.globalData.userInfo = {
      userId: userInfo.userId,
      openid: userInfo.openid,
      companyCode: userInfo.companyCode,
      hasCardInfo: userInfo.hasCardInfo
    };
    
    // 更新本地存储
    wx.setStorageSync('userInfo', app.globalData.userInfo);
    
    return {
      code: 0,
      data: userInfo
    };
  } catch (err) {
    console.error('获取用户信息失败', err);
    throw err;
  }
};

/**
 * 确保用户已登录
 * @returns {Promise} 用户信息promise
 */
const ensureLoggedIn = async () => {
  if (isLoggedIn()) {
    return app.globalData.userInfo;
  }
  
  try {
    return await doLogin();
  } catch (err) {
    console.error('确保登录失败', err);
    throw err;
  }
};

/**
 * 退出登录
 */
const logout = () => {
  app.globalData.token = '';
  app.globalData.userInfo = null;
  app.globalData.cardInfo = null;
  app.globalData.companyInfo = null;
  wx.removeStorageSync('token');
  wx.removeStorageSync('userInfo');
  
  // 跳转到首页
  wx.switchTab({
    url: '/pages/personal/index'
  });
};

/**
 * 用户登录
 * @returns {Promise} 登录结果promise
 */
const login = async () => {
  try {
    const userInfo = await doLogin();
    return {
      code: 0,
      data: userInfo
    };
  } catch (err) {
    console.error('登录失败', err);
    throw err;
  }
};

module.exports = {
  isLoggedIn,
  doLogin,
  getUserInfo,
  ensureLoggedIn,
  logout,
  login
}; 