/**
 * 企业路由
 */
const express = require('express');
const router = express.Router();
const companyController = require('../controllers/company.controller');
const { verifyToken, optionalVerifyToken } = require('../middlewares/auth.middleware');

// 获取企业信息 - 添加中间件判断
// 当URL包含companyCode参数时，使用optionalVerifyToken允许未登录访问
router.get('/info', (req, res, next) => {
  if (req.query.companyCode) {
    // 通过companyCode查询企业时不需要强制登录
    optionalVerifyToken(req, res, next);
  } else {
    // 查询当前用户关联的企业时需要登录验证
    verifyToken(req, res, next);
  }
}, companyController.getCompanyInfo);

// 更新企业页面路径
router.put('/pages', verifyToken, companyController.updateCompanyPages);

// 更新企业自定义名称
router.put('/custom-names', verifyToken, companyController.updateCustomNames);

// 更新企业分享图片
router.put('/share-images', verifyToken, companyController.updateShareImages);

// 搜索企业名称 - 不需要登录验证，支持公开搜索
router.get('/search', companyController.searchCompanies);

module.exports = router;