Component({
  /**
   * 组件的属性列表
   */
  properties: {
    label: {
      type: String,
      value: ''
    },
    placeholder: {
      type: String,
      value: '请输入'
    },
    value: {
      type: String,
      value: ''
    },
    name: {
      type: String,
      value: ''
    },
    required: {
      type: <PERSON>olean,
      value: false
    },
    type: {
      type: String,
      value: 'text'
    },
    maxlength: {
      type: Number,
      value: 140
    },
    disabled: {
      type: Boolean,
      value: false
    },
    focus: {
      type: Boolean,
      value: false
    },
    clearable: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showClear: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 输入事件
    onInput(e) {
      const { value } = e.detail;
      this.setData({
        showClear: this.properties.clearable && value
      });
      this.triggerEvent('input', { name: this.properties.name, value });
    },
    
    // 清空输入
    onClear() {
      this.setData({
        showClear: false
      });
      this.triggerEvent('input', { name: this.properties.name, value: '' });
    },
    
    // 聚焦事件
    onFocus() {
      this.setData({
        showClear: this.properties.clearable && this.properties.value
      });
    },
    
    // 失焦事件
    onBlur() {
      this.setData({
        showClear: false
      });
      this.triggerEvent('blur', { name: this.properties.name, value: this.properties.value });
    }
  }
}) 