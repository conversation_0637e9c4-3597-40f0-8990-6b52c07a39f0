# Token过期模态框问题修复测试指南

## 问题描述
用户运行 `debugTokenExpiry.simulateTokenExpiry()` 后，刷新页面提示"登录已过期"，点击重新登录并成功登录后，个人名片页面上仍然显示"登录已过期"的弹窗，需要再次点击才能关闭。

## 问题根本原因
`wx.showModal` 一旦显示就无法通过代码关闭，即使用户已经重新登录成功，之前显示的模态框仍然会保留在页面上。

## 修复方案

### 1. 添加模态框状态管理
- 新增 `isShowingTokenExpiredModal` 标志跟踪模态框状态
- 防止重复显示模态框
- 在用户重新登录后自动重置状态

### 2. 延迟检查机制
- 在显示模态框前延迟200ms再次检查登录状态
- 如果用户已经重新登录，取消模态框显示
- 使用定时器确保检查的准确性

### 3. 定时器管理
- 添加定时器清理机制
- 在页面销毁时清理定时器
- 在状态同步时取消不必要的定时器

## 修复内容

### 文件: `pages/personal/index.js`

#### 1. 数据结构修改
```javascript
data: {
  // ... 其他字段
  isShowingTokenExpiredModal: false // 新增：标记是否正在显示token过期模态框
}
```

#### 2. handleTokenExpired 方法优化
- 添加重复显示检查
- 延迟显示模态框并在显示前再次检查登录状态
- 使用定时器管理模态框显示时机

#### 3. syncLoginStatus 方法优化
- 检测到用户重新登录时自动取消模态框显示
- 清理相关定时器
- 重置模态框状态

#### 4. 页面生命周期管理
- 添加 `onUnload` 方法清理定时器
- 防止内存泄漏

## 测试步骤

### 测试场景1：模拟token过期后重新登录（主要场景）
1. 在控制台运行：
   ```javascript
   debugTokenExpiry.simulateTokenExpiry()
   ```
2. 刷新页面，观察是否弹出"登录已过期"提示
3. 点击"重新登录"按钮
4. 在登录页面完成登录
5. 观察返回个人页面后的状态

**预期结果**：
- ✅ 登录成功后不应该再显示"登录已过期"模态框
- ✅ 页面直接显示已登录状态
- ✅ 不需要再次点击模态框按钮

### 测试场景2：快速重新登录
1. 运行 `debugTokenExpiry.simulateTokenExpiry()`
2. 在模态框显示前快速重新登录（可以在控制台直接设置token）
3. 观察模态框是否还会显示

**预期结果**：
- ✅ 如果用户已经重新登录，模态框不应该显示

### 测试场景3：重复token过期
1. 模拟token过期
2. 不点击模态框，再次触发token过期
3. 观察是否会显示多个模态框

**预期结果**：
- ✅ 不应该显示重复的模态框

## 调试信息

修复后会输出以下调试信息：

```
处理token失效，清理状态并提示用户重新登录
检测到用户已重新登录，取消token过期模态框显示
已清理token过期检查定时器
用户已重新登录，取消显示token过期模态框
页面销毁，清理token过期检查定时器
```

## 使用调试工具测试

### 1. 检查模态框状态
```javascript
// 检查当前页面的模态框状态
const pages = getCurrentPages();
const currentPage = pages[pages.length - 1];
console.log('模态框状态:', currentPage.data.isShowingTokenExpiredModal);
```

### 2. 手动重置模态框状态
```javascript
// 如果模态框卡住了，可以手动重置
const pages = getCurrentPages();
const currentPage = pages[pages.length - 1];
currentPage.setData({
  isShowingTokenExpiredModal: false
});
```

### 3. 完整测试流程
```javascript
// 1. 模拟token过期
debugTokenExpiry.simulateTokenExpiry();

// 2. 等待几秒后检查状态
setTimeout(() => {
  debugTokenExpiry.checkStateConsistency();
}, 3000);

// 3. 强制同步状态（模拟重新登录）
setTimeout(() => {
  const app = getApp();
  app.globalData.token = 'valid_token';
  app.globalData.userInfo = { userId: 1, openid: 'test' };
  wx.setStorageSync('token', 'valid_token');
  wx.setStorageSync('userInfo', { userId: 1, openid: 'test' });
  
  debugTokenExpiry.forceSyncPageStatus();
}, 5000);
```

## 关键修复点

1. **防止重复显示**：通过状态标志避免多个模态框
2. **延迟检查**：在显示前再次确认是否需要显示
3. **自动取消**：检测到重新登录时自动取消显示
4. **定时器管理**：正确清理定时器防止内存泄漏

## 注意事项

1. **微信小程序限制**：`wx.showModal` 无法通过代码关闭，只能通过预防机制
2. **时机控制**：延迟时间设置为200ms，平衡响应速度和检查准确性
3. **状态同步**：确保页面状态与全局状态保持一致

## 如果问题仍然存在

如果修复后仍然出现模态框残留问题：

1. **检查定时器**：确认定时器是否正确清理
2. **手动重置**：使用调试工具手动重置状态
3. **重新加载页面**：使用 `wx.reLaunch` 强制重新加载
