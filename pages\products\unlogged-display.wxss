.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0;
  min-height: 100vh;
  background-color: #F5F6FA;
}

.default-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  width: 100%;
  height: 100vh;
  border-radius: 0;
  background-color: #FFFFFF;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.content-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 60rpx 40rpx;
  flex: 1;
  justify-content: center;
}

.default-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 40rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.default-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
  text-align: center;
}

.default-subtitle {
  font-size: 30rpx;
  color: #666666;
  margin-bottom: 60rpx;
  text-align: center;
  line-height: 1.6;
  max-width: 600rpx;
}

.contact-qrcode {
  width: 260rpx;
  height: 260rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.qrcode-tip {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 40rpx;
}

.action-btn-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  padding: 40rpx;
  padding-bottom: 80rpx;
  background-color: #FFFFFF;
  border-top: 1rpx solid #EEEEEE;
  margin-top: -60rpx;
}

.action-btn {
  width: 90%;
  height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  line-height: 90rpx;
  text-align: center;
  background-color: #3381FF;
  color: white;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  letter-spacing: 2rpx;
  box-shadow: 0 6rpx 12rpx rgba(51, 129, 255, 0.2);
}

.action-btn-secondary {
  background-color: #F5F6FA;
  color: #666666;
  border: 1rpx solid #DDDDDD;
  box-shadow: none;
  margin-bottom: 0;
} 