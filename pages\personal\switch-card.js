const app = getApp();
const cardStore = require('../../store/card');

Page({
  data: {
    cards: [], // 用户的所有名片列表
    currentCardId: '', // 当前选中的名片ID
    isLoading: true, // 加载状态
    isEmpty: false, // 是否没有名片
    needRefresh: false
  },

  onLoad: function(options) {
    this.fetchCardList();
  },

  // 获取用户的所有名片
  async fetchCardList() {
    this.setData({ isLoading: true });
    
    try {
      const res = await cardStore.getAllCards();
      
      console.log('获取名片列表成功:', res);
      
      // 根据实际返回值格式调整判断条件
      if (res && res.list && res.list.length > 0) {
        // 标准化数据，处理字段名不一致的问题
        const normalizedList = res.list.map(card => {
          // 确保is_default和isDefault字段都存在
          if (card.isDefault !== undefined && card.is_default === undefined) {
            card.is_default = card.isDefault === 1;
          } else if (card.is_default !== undefined && card.isDefault === undefined) {
            card.isDefault = card.is_default ? 1 : 0;
          }
          return card;
        });
        
        // 找到当前默认名片
        const currentCard = normalizedList.find(card => card.is_default || card.isDefault === 1) || normalizedList[0];
        
        this.setData({
          cards: normalizedList,
          currentCardId: currentCard.id || '',
          isEmpty: false,
          isLoading: false
        });
      } else {
        // 没有名片或获取失败
        this.setData({
          cards: [],
          currentCardId: '',
          isEmpty: true,
          isLoading: false
        });
      }
    } catch (error) {
      console.error('获取名片列表失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      this.setData({
        isLoading: false,
        isEmpty: true
      });
    }
  },

  // 选择名片并设为默认
  async selectCard(e) {
    const cardId = e.currentTarget.dataset.id;
    
    if (cardId === this.data.currentCardId) {
      // 如果点击的是当前默认名片，不做操作
      return;
    }

    wx.showLoading({
      title: '正在切换...',
    });

    try {
      const res = await cardStore.setDefaultCard(cardId);
      
      wx.hideLoading();
      
      // 检查返回结果
      if (res) {
        // 更新本地数据
        const cards = this.data.cards.map(card => {
          const isDefault = card.id === cardId;
          return {
            ...card,
            is_default: isDefault,
            isDefault: isDefault ? 1 : 0
          };
        });
        
        this.setData({
          cards: cards,
          currentCardId: cardId
        });
        
        // 更新全局数据
        app.globalData.cardInfo = cards.find(card => card.id === cardId);
        
        wx.showToast({
          title: '切换成功',
          icon: 'success'
        });
        
        // 设置上一页需要刷新
        const pages = getCurrentPages();
        if (pages.length > 1) {
          const prevPage = pages[pages.length - 2];
          prevPage.setData({
            needRefresh: true
          });
        }
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1000);
      } else {
        wx.showToast({
          title: '切换失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('切换名片失败:', error);
      wx.showToast({
        title: error.message || '网络错误，请重试',
        icon: 'none'
      });
    }
  },

  // 创建新名片
  createNewCard() {
    wx.navigateTo({
      url: '/pages/personal/create-card'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.fetchCardList();
    wx.stopPullDownRefresh();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  onShow() {
    // 如果需要刷新数据
    if (this.data.needRefresh) {
      this.setData({
        needRefresh: false
      });
      this.fetchCardList();
    }
  },
  
  /**
   * 编辑名片
   */
  editCard(e) {
    const cardId = e.currentTarget.dataset.cardId;
    wx.navigateTo({
      url: `/pages/personal/edit?id=${cardId}`
    });
    
    // 标记需要刷新
    this.setData({
      needRefresh: true
    });
  },
  
  /**
   * 删除名片
   */
  deleteCard(e) {
    const cardId = e.currentTarget.dataset.cardId;
    
    // 删除最后一张名片的限制已移除
    
    // 确认删除
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张名片吗？删除后无法恢复。',
      confirmColor: '#3E7FFF',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中',
            mask: true
          });
          
          try {
            const res = await cardStore.deleteCard(cardId, true); // 强制删除，即使是最后一张名片
            
            // 从列表中移除
            const updatedList = this.data.cards.filter(card => card.id !== cardId);
            const isCurrentDefault = cardId === this.data.currentCardId;
            let newCurrentId = this.data.currentCardId;
            
            // 如果删除的是当前默认名片，设置新的默认名片
            if (isCurrentDefault && updatedList.length > 0) {
              newCurrentId = updatedList[0].id;
              
              // 调用API设置新的默认名片
              try {
                await cardStore.setDefaultCard(newCurrentId);
                
                // 更新本地数据中的默认状态
                updatedList.forEach(card => {
                  card.is_default = card.id === newCurrentId;
                  card.isDefault = card.id === newCurrentId ? 1 : 0;
                });
              } catch (err) {
                console.error('设置新的默认名片失败', err);
              }
            }
            
            this.setData({
              cards: updatedList,
              currentCardId: updatedList.length > 0 ? newCurrentId : '',
              isEmpty: updatedList.length === 0
            });
            
            wx.hideLoading();
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
            
            // 设置上一页需要刷新
            const pages = getCurrentPages();
            if (pages.length > 1) {
              const prevPage = pages[pages.length - 2];
              prevPage.setData({
                needRefresh: true
              });
            }
          } catch (error) {
            console.error('删除名片失败', error);
            wx.hideLoading();
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
}); 