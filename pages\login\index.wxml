<view class="login-page-container">
  <view class="login-container">
    <!-- 登录页头部 -->
    <view class="login-header">
      <image class="logo" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png" mode="aspectFit"></image>
      <text class="title">智链名片</text>
      <text class="subtitle">专业的企业名片管理平台</text>
    </view>

    <!-- 功能特性介绍 -->
    <view class="login-features">
      <view class="feature-item">
        <image class="feature-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png" mode="aspectFit"></image>
        <text class="feature-text">随时随地分享电子名片，告别传统纸质名片</text>
      </view>
      <view class="feature-item">
        <image class="feature-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/card-folder.png" mode="aspectFit"></image>
        <text class="feature-text">高效管理客户信息，构建个人专属人脉网络</text>
      </view>
      <view class="feature-item">
        <image class="feature-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-default.png" mode="aspectFit"></image>
        <text class="feature-text">展示企业风采，提升品牌影响力</text>
      </view>
    </view>

    <!-- 登录页底部 -->
    <view class="login-footer">
      <button class="login-btn {{isAgreementChecked ? 'login-btn-active' : 'login-btn-disabled'}}" open-type="getUserInfo" bindgetuserinfo="login" disabled="{{!isAgreementChecked}}">
        <image class="wechat-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/wechat.png" mode="aspectFit"></image>
        <text class="login-text">微信登录</text>
      </button>
      <view class="agreement-container">
        <view class="agreement-content">
          <checkbox class="agreement-checkbox" checked="{{isAgreementChecked}}" bindtap="toggleAgreement"></checkbox>
          <view class="privacy-text">已阅读并同意<text class="privacy-link" bindtap="goToServiceAgreement">《用户服务协议》</text>与<text class="privacy-link" bindtap="goToPrivacyPolicy">《隐私政策》</text></view>
        </view>
      </view>
    </view>
  </view>
</view> 