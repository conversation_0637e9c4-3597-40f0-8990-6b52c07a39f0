Component({
  /**
   * 组件的属性列表
   */
  properties: {
    cards: {
      type: Array,
      value: []
    },
    loading: {
      type: Boolean,
      value: false
    },
    empty: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    defaultAvatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-avatar.png'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击名片项
    onCardTap(e) {
      const { id } = e.currentTarget.dataset;
      this.triggerEvent('cardtap', { id });
    },
    
    // 删除名片
    onDeleteCard(e) {
      const { id, collectionId } = e.currentTarget.dataset;
      wx.showModal({
        title: '提示',
        content: '确定要删除这张名片吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete', { id, collectionId });
          }
        }
      });
    }
  }
}) 