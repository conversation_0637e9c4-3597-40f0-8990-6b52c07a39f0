const app = getApp();
const { get, post, delete: del } = require('./request');

/**
 * 检查名片是否已收藏
 * @param {number} cardId - 名片ID
 * @returns {Promise} 收藏状态promise (返回true/false)
 */
const checkCollectionStatus = async (cardId) => {
  try {
    if (!app.globalData.userInfo || !app.globalData.userInfo.userId) {
      // 用户未登录，肯定未收藏
      console.log('用户未登录，返回未收藏状态');
      return {
        code: 0,
        data: false
      };
    }
    
    console.log(`检查名片收藏状态, 卡片ID: ${cardId}, 用户ID: ${app.globalData.userInfo.userId}`);
    const url = `/collection/check?cardId=${cardId}`;
    
    try {
      const result = await get(url);
      console.log("收藏状态检查API返回:", result);
      return {
        code: 0,
        data: result.isCollected
      };
    } catch (err) {
      // API调用失败，记录错误但返回未收藏
      console.error('API调用检查名片收藏状态失败', err);
      return {
        code: 0,
        data: false
      };
    }
  } catch (err) {
    // 任何其他错误，记录但返回未收藏
    console.error('检查名片收藏状态过程中发生错误', err);
    return {
      code: 0,
      data: false
    };
  }
};

/**
 * 收藏名片
 * @param {number} cardId - 名片ID
 * @returns {Promise} 收藏结果promise
 */
const collectCard = async (cardId) => {
  try {
    const result = await post('/collection', { cardId });
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('收藏名片失败', err);
    // 返回更具体的错误信息
    return {
      code: err.code || 500,
      message: err.message || '收藏名片失败'
    };
  }
};

/**
 * 获取名片夹列表
 * @param {number} [page=1] - 页码
 * @param {number} [pageSize=20] - 每页数量
 * @param {string} [keyword=''] - 搜索关键词
 * @returns {Promise} 名片夹列表promise
 */
const getCardFolder = async (page = 1, pageSize = 20, keyword = '') => {
  try {
    const params = { page, pageSize };
    if (keyword) {
      params.keyword = keyword;
    }
    
    const result = await get('/collection', params);
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('获取名片夹列表失败', err);
    throw err;
  }
};

/**
 * 删除收藏的名片
 * @param {number} collectionId - 收藏记录ID
 * @returns {Promise} 删除结果promise
 */
const removeCollection = async (collectionId) => {
  try {
    const result = await del(`/collection/${collectionId}`);
    return {
      code: 0,
      data: result
    };
  } catch (err) {
    console.error('删除收藏名片失败', err);
    throw err;
  }
};

module.exports = {
  checkCollectionStatus,
  collectCard,
  getCardFolder,
  removeCollection
}; 